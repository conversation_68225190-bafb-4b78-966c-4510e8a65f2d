[{"symbol_name": "http", "symbol_type": "const", "module_name": "http", "is_default": false, "is_ets": false, "description": "`options` in `socket.connect()` are also supported. Node.js maintains several connections per server to make HTTP requests. This function allows one to transparently issue requests. `url` can be a string or a `URL` object. If `url` is a string, it is automatically parsed with `new URL()`. If it is a `URL` object, it will be automatically converted to an ordinary `options` object. If both `url` and `options` are specified, the objects are merged, with the`options` properties taking precedence. The optional `callback` parameter will be added as a one-time listener for the `'response'` event. `http.request()` returns an instance of the {@link ClientRequest} class. The `ClientRequest` instance is a writable stream. If one needs to upload a file with a POST request, then write to the `ClientRequest` object. ```js", "import_statement": "import { http } from 'http';", "parent_symbol": null, "is_nested": false, "score": 0.69954216}, {"symbol_name": "http2", "symbol_type": "const", "module_name": "http2", "is_default": false, "is_ets": false, "description": "Returns a `ClientHttp2Session` instance. ```js", "import_statement": "import { http2 } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.6740436}, {"symbol_name": "http", "symbol_type": "export_assignment", "module_name": "@ohos.net.http", "is_default": true, "is_ets": false, "description": "Export assignment", "import_statement": "import http from '@ohos.net.http';", "parent_symbol": null, "is_nested": false, "score": 0.64190924}]