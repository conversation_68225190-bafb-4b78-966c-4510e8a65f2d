[{"symbol_name": "WebHttpBodyStream", "symbol_type": "class", "module_name": "@ohos.web.webview", "is_default": false, "is_ets": false, "description": "The http body stream of the request. @syscap SystemCapability.Web.Webview.Core @atomicservice @since 12", "import_statement": "import { webview.WebHttpBodyStream } from '@ohos.web.webview';", "parent_symbol": "webview", "is_nested": true, "full_name": "webview.WebHttpBodyStream", "score": 0.6861857}, {"symbol_name": "File", "symbol_type": "class", "module_name": "buffer", "is_default": false, "is_ets": false, "description": "A [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File) provides information about files. @since v19.2.0, v18.13.0", "import_statement": "import { File } from 'buffer';", "parent_symbol": null, "is_nested": false, "score": 0.5997384}, {"symbol_name": "Blob", "symbol_type": "class", "module_name": "buffer", "is_default": false, "is_ets": false, "description": "A [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob) encapsulates immutable, raw data that can be safely shared across multiple worker threads. @since v15.7.0, v14.18.0", "import_statement": "import { Blob } from 'buffer';", "parent_symbol": null, "is_nested": false, "score": 0.5881238}]