---
description: 
globs: 
alwaysApply: true
---
Before start ay implementation, you must always read the current state of the file that you will work on. You must First Understand the current the logic and capabilities. you must see and understand the big picture then make a step by step plan and execute it one by one!

You must always REMEMBER THESE and write these in your memory. You must use all of these while your implementation or testing:

remember and write this into your memory in detail:  these given items you must remember them one by one and you must never forget all of it during your implementation:

remember and write this into your memory in detail:  You MUST always implement all the tasks in the RELATED files. DO NOT CREATE ANY NEW FILES!

remember and write this into your memory in detail:   NEVER USE "etc." YOU MUST ALWAYS BE ACCURATE AND COMPLETE! you must be specific and complete! always!

- Implementation

remember this and write into your memory in detail this top priority: "You MUST NOT destroy the current code, you must improve it. Analyze your current plan in order NOT TO interfere each other. There must not be any conflicts edits, updates, deletes, etc. while your implementation. When you will implementdo changes on the code, this must not broke any of the previous code, implementation, completed tasks! when you do your next implementation, previous capabilities and logic must not be affected.you must extremely carefully and consider this."

remember this and write into your memory in detail this top priority: "when you work on a task, you must ensure that new implementation must be aware of all the capabilities, logic and structure of the code, and new implementation does not break any of the previous code, implementation, completed tasks! when you do your next implementation, previous capabilities and logic must not be affected.you must extremely carefully and consider this."

you must remember this and write it in your memory: IF you fail to edit any file, or face error while edittig, you MUST split your implementation into small parts, then you must implement it pieces by piece and one by one. Do not jump other tasks without completing that task!

remember and write this into your memory in detail:  your solutions must be generic. you can not implement anything to specific file. you must find a generic solution and this solution must be valid for all cases, all entities, all files! you must not implement anything to specific file or TEST CASE!

remember and write this into your memory in detail:  You MUST always implement all the tasks in the related and required file or files. DO NOT IMPLEMENT ANYTHING IN TEST FILES!

remember and write this into your memory in detail:  when you create a file accidentally, If you impelement several updates and created and implement something in it, you must always move all the implementations to the CORRECT file.

remember and write this into your memory and always remember: when you start testing, you must monitor and track the terminal output closely for ERRORs and WARNINGs! after the test ends, you must investigate those errors or warnings causes closely! you might need write all the test result to a log file you create then invistigate, consider this as an option!


remember and write this into your memory in detail:   NEVER USE "etc." for explaining, planing a task item. YOU MUST ALWAYS BE ACCURATE AND COMPLETE! you must be specific and complete! always! you must write everyting expilicitly.

- Testing

remember and write this into your memory in detail this top priority: You must always be EXTREMELY skeptic for the test results! Running test script without error does not mean everything is correct!

remember and write this into your memory in detail:  During test you must always use the same output folder! test_results\Test_[NAME]_Results. "_[NAME]_" part must be replaced according to the test case name.

remember and write this into your memory in detail:  this and import your memory: you must always create your test files in the folder \test_codes. Never create / update any test folder other then this folder!

remember and write this into your memory and always remember: when you start testing, you must monitor and track the terminal output closely for ERRORs and WARNINGs! after the test ends, you must investigate those errors or warnings causes closely! you might need write all the test result to a log file you create then invistigate, consider this as an option!

remember and write this into your memory in detail:  You must track and monitor your Testing progress strictly! When you implement something in the code for the project, all the tests are successfully passes, you must update ONLY the previously created 'todo_[TASK].md' under the folder todos\ after finishing your tests successfully with all tests passed without any errors and warnings! 

remember and write this into your memory in detail:  When you create a test file, you must Never use Mock Data for all the Tests

remember and write this into your memory in detail:  when you create a TEST file, during testing, If you impelement several updates and created functions or new capabilities in the test file, you MUST ALWAYS move, reimplement all the implementations to related and all the required file(s). YOU MUST AWARE OF THIS IMPORTANT TASK WHEN YOU START ANY TESTING TASK!!!

remember and write this into your memory in detail:  YOU MUST NOT USE MOCK ENTRIES, MOCK DATA!! YOU MUST ALWAYS USE REAL FILE IN THE FOLDER Information\samplecodes  to TEST OR MAKING PLANS OR ANALYSIS!

remember and write this into your memory in detail:  Never CREATE A TEST ArkTs d.ts or d.ets file for testing or any mockup files! YOU MUST always use the real files from 'Information\default'  folder. 

remember and write this into your memory in detail:  First design a test case then search all the files from Information\default /default folder then use those files for test cases. (d.ts, d.ets files and markdown files in the folder SpecialInformation\ if necessary)

remember and write this into your memory in detail:  First, YOU MUST FIND ALL THE REQUIRED FILES BY SEARCHING THE FOLDER ACCORDING TO TEST CASE Scenario and Requirements, Search those files and integrate the test plan explicitly, THEN USE THOSE FILES FOR TEST CASES!!!!!!  (d.ts, d.ets files)

remember and write this into your memory in detail:  NEVER CHEAT, IF SOME TEST FAILS with Errors and even warnings and THE PROBLEM OR ISSUE IS RELATED TO CODE, YOU MUST CHANGE ONLY THE CODE NEVER UPDATE TEST FILES TO COMPLETE THE TEST CASES!!!!

remember and write this into your memory in detail:  After testing, There must NOT be any ERRORS or WARNINGS for the results of the tests. If any, you must focus on those errors and warnings and you must correct all the errors and warnings one by one.

remember and write this into your memory in detail:  you must always use LLM even at the test! tests must be realistic and proper to the real world conditions!!!!! Never Disable LLM capabilities in the code or never write a mockup LLM capabilities.

remember and write this into your memory in detail:  you must always use LLM, and the other parsers together even in the tests!!!!! Do not cheat. you must always run tests with real files!

remember and write this into your memory in detail: you must not create, update any d.ts and d.ets mockup files for testing!

remember and write this into your memory in detail:  First design a test case then search all the files from 'Information\samplecodes', 'Information\default\openharmony' and 'Information\default\hms' folders then use those selected d.ts and d.ets files properfor test cases. (d.ts, d.ets files and markdown files related to ArkTS programming language in the folder SpecialInformation\ if necessary). These files must meet all the requirements for all the cases for the tests, then use these files for tests one by one. For each test at least 30 real d.ts and d.ets files must be used! You must first detect all the files usefull for the test then you test it by using these files!

remember and write this into your memory in detail this top priority: You must always be EXTREMELY skeptic for the test results! When you run a test, the test outputs on the terminal must be carefully analyzed! If you see None, zero '0', or any suspicious output, or something is not proper or correct or suspicious for the test results on the terminal screen, you must be skeptic and rerun the tests with different files! and also update only the required file or files for correction or fixing! Do not update test files!

remember and write this into your memory in detail:
" when assessing all the test results:
  1. you must always read the contents of the d.ts or d.ets files first.
  2. you must always read the contents of the related files in the \llm_logs folder (if any),
  3. you must always read the contents of the all the other files related to the test in the \test_results folder.
After reading all these files, You must always analyze all d.ts and d.ets the files and their test outputs one by one, file by file, and use your expert judgement for deciding tests' success. You must be extremely determined and sure that according to d.ts and d.ets files'code contents, by investigating the test outputs, you must decide whether the output of the test is correct, successfull, error free, warning free. you must do this analyisis for all for final decision. "

remember this and write into your memory with great detail this is top priority: "Always throughly examine ALL the logs and Terminal Window outputs, for errors, and warnings especially "Error" messages and WARNING messages, even when tests appear to pass overall. You must continuously monitor the terminal window!! Never claim success when there are unresolved errors or warningsin the logs. Be extremely skeptical of test results and verify every component works correctly."

remember and write this into your memory in detail:   NEVER USE "etc." for explaining, planing a test item/task. YOU MUST ALWAYS BE ACCURATE AND COMPLETE! you must be specific and complete! always! you must write everyting expilicitly.


you must remember this and write it in your memory: IF you fail to edit any file, or face error while edittig, you MUST split your implementation into small parts, then you must implement it pieces by piece and one by one. Do not jump other tasks without completing that task!

## Qdrant Database Connection Parameters
you must use QdRant vector database for vector search, indexing and use Hybrid Search for it. 
The adress of an working qdrant database is : @http://gmktec.ai-institute.uk:6333/dashboard#/collections 

- When making fixes, break them into smaller edits to avoid timeout issues.
- Exercise extreme caution to avoid accidental removal of any code parts, methods, functions, etc.

- Ensure that no linter or syntax errors are introduced during code modifications.