[{"symbol_name": "<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@system.prompt", "is_default": false, "is_ets": false, "description": "Defines the prompt info of button. @interface But<PERSON> @syscap SystemCapability.ArkUI.ArkUI.Full @atomicservice @since 11", "import_statement": "import { Button } from '@system.prompt';", "parent_symbol": null, "is_nested": false, "score": 0.69622564}, {"symbol_name": "ButtonElement", "symbol_type": "interface", "module_name": "js.viewmodel", "is_default": false, "is_ets": false, "description": "The <button> component includes capsule, circle, text, arc, and download buttons. @interface ButtonElement @syscap SystemCapability.ArkUI.ArkUI.Full @since 4", "import_statement": "import { ButtonElement } from 'js.viewmodel';", "parent_symbol": null, "is_nested": false, "score": 0.6844307}, {"symbol_name": "ActionButton", "symbol_type": "interface", "module_name": "@ohos.reminderAgent", "is_default": false, "is_ets": false, "description": "Action button information. The button will show on displayed reminder. @interface ActionButton @syscap SystemCapability.Notification.ReminderAgent @since 7 @deprecated since 9 @useinstead reminderAgentManager.ActionButton", "import_statement": "import { reminderAgent.ActionButton } from '@ohos.reminderAgent';", "parent_symbol": "reminderAgent", "is_nested": true, "full_name": "reminderAgent.ActionButton", "score": 0.65438074}]