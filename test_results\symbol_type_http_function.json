[{"symbol_name": "getPackedSettings", "symbol_type": "function", "module_name": "http2", "is_default": false, "is_ets": false, "description": "Returns a `Buffer` instance containing serialized representation of the given HTTP/2 settings as specified in the [HTTP/2](https://tools.ietf.org/html/rfc7540) specification. This is intended for use with the `HTTP2-Settings` header field. ```js const http2 = require('node:http2'); const packed = http2.getPackedSettings({ enablePush: false }); console.log(packed.toString('base64')); // Prints: AAIAAAAA ``` @since v8.4.0", "import_statement": "import { getPackedSettings } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.6419869}, {"symbol_name": "createServer", "symbol_type": "function", "module_name": "http2", "is_default": false, "is_ets": false, "description": "Returns a `net.Server` instance that creates and manages `Http2Session`instances. Since there are no browsers known that support [unencrypted HTTP/2](https://http2.github.io/faq/#does-http2-require-encryption), the use of {@link createSecureServer} is necessary when communicating with browser clients. ```js const http2 = require('node:http2'); // Create an unencrypted HTTP/2 server. // Since there are no browsers known that support // unencrypted HTTP/2, the use of `http2.createSecureServer()` // is necessary when communicating with browser clients. const server = http2.createServer(); server.on('stream', (stream, headers) => { stream.respond({ 'content-type': 'text/html; charset=utf-8', ':status': 200, }); stream.end('<h1>Hello World</h1>'); }); server.listen(8000); ``` @since v8.4.0 @param onRequestHandler See `Compatibility API` export function createServer(onRequestHandler?: (request: Http2ServerRequest, response: Http2ServerResponse) => void): Http2Server;", "import_statement": "import { createServer } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.6309867}, {"symbol_name": "createSecureServer", "symbol_type": "function", "module_name": "http2", "is_default": false, "is_ets": false, "description": "Returns a `tls.Server` instance that creates and manages `Http2Session`instances. ```js const http2 = require('node:http2'); const fs = require('node:fs'); const options = { key: fs.readFileSync('server-key.pem'), cert: fs.readFileSync('server-cert.pem'), }; // Create a secure HTTP/2 server const server = http2.createSecureServer(options); server.on('stream', (stream, headers) => { stream.respond({ 'content-type': 'text/html; charset=utf-8', ':status': 200, }); stream.end('<h1>Hello World</h1>'); }); server.listen(8443); ``` @since v8.4.0 @param onRequestHandler See `Compatibility API` export function createSecureServer(onRequestHandler?: (request: Http2ServerRequest, response: Http2ServerResponse) => void): Http2SecureServer;", "import_statement": "import { createSecureServer } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.60737175}]