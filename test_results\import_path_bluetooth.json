[{"symbol_name": "bluetooth", "symbol_type": "export_assignment", "module_name": "@ohos.bluetooth", "is_default": true, "is_ets": false, "description": "Export assignment", "import_statement": "import bluetooth from '@ohos.bluetooth';", "parent_symbol": null, "is_nested": false, "score": 0.6196252}, {"symbol_name": "bluetoothManager", "symbol_type": "export_assignment", "module_name": "@ohos.enterprise.bluetoothManager", "is_default": true, "is_ets": false, "description": "Export assignment", "import_statement": "import bluetoothManager from '@ohos.enterprise.bluetoothManager';", "parent_symbol": null, "is_nested": false, "score": 0.58907783}, {"symbol_name": "bluetoothManager", "symbol_type": "export_assignment", "module_name": "@ohos.bluetoothManager", "is_default": true, "is_ets": false, "description": "Export assignment", "import_statement": "import bluetoothManager from '@ohos.bluetoothManager';", "parent_symbol": null, "is_nested": false, "score": 0.58907783}]