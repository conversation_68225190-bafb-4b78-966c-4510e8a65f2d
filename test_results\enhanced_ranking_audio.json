{"standard": [{"symbol_name": "Audio", "symbol_type": "class", "module_name": "@ohos.data.unifiedDataChannel", "is_default": false, "is_ets": false, "description": "Describe the unified audio data @extends File @syscap SystemCapability.DistributedDataManager.UDMF.Core @atomicservice @since 11", "import_statement": "import { unifiedDataChannel.Audio } from '@ohos.data.unifiedDataChannel';", "parent_symbol": "unifiedDataChannel", "is_nested": true, "full_name": "unifiedDataChannel.Audio", "score": 0.67479736}, {"symbol_name": "audio", "symbol_type": "export_assignment", "module_name": "@ohos.multimedia.audio", "is_default": true, "is_ets": false, "description": "Export assignment", "import_statement": "import audio from '@ohos.multimedia.audio';", "parent_symbol": null, "is_nested": false, "score": 0.64458895}, {"symbol_name": "AudioPlayer", "symbol_type": "interface", "module_name": "@ohos.multimedia.media", "is_default": false, "is_ets": false, "description": "Manages and plays audio. Before calling an AudioPlayer method, you must use createAudioPlayer() to create an AudioPlayer instance. @typedef AudioPlayer @syscap SystemCapability.Multimedia.Media.AudioPlayer @since 6 @deprecated since 9 @useinstead ohos.multimedia.media/media.AVPlayer", "import_statement": "import { media.AudioPlayer } from '@ohos.multimedia.media';", "parent_symbol": "media", "is_nested": true, "full_name": "media.AudioPlayer", "score": 0.62260807}], "enhanced": [{"symbol_name": "Audio", "symbol_type": "class", "module_name": "@ohos.data.unifiedDataChannel", "is_default": false, "is_ets": false, "description": "Describe the unified audio data @extends File @syscap SystemCapability.DistributedDataManager.UDMF.Core @atomicservice @since 11", "import_statement": "import { unifiedDataChannel.Audio } from '@ohos.data.unifiedDataChannel';", "parent_symbol": "unifiedDataChannel", "is_nested": true, "full_name": "unifiedDataChannel.Audio", "score": 0.772358152, "vector_score": 0.67479736, "text_score": 1.0}, {"symbol_name": "AudioPlayer", "symbol_type": "interface", "module_name": "@ohos.multimedia.media", "is_default": false, "is_ets": false, "description": "Manages and plays audio. Before calling an AudioPlayer method, you must use createAudioPlayer() to create an AudioPlayer instance. @typedef AudioPlayer @syscap SystemCapability.Multimedia.Media.AudioPlayer @since 6 @deprecated since 9 @useinstead ohos.multimedia.media/media.AVPlayer", "import_statement": "import { media.AudioPlayer } from '@ohos.multimedia.media';", "parent_symbol": "media", "is_nested": true, "full_name": "media.AudioPlayer", "score": 0.7358256489999999, "vector_score": 0.62260807, "text_score": 1.0}, {"symbol_name": "AudioRecorderConfig", "symbol_type": "interface", "module_name": "@ohos.multimedia.media", "is_default": false, "is_ets": false, "description": "Provides the audio recorder configuration definitions. @typedef AudioRecorderConfig @syscap SystemCapability.Multimedia.Media.AudioRecorder @since 6 @deprecated since 9 @useinstead ohos.multimedia.media/media.AVRecorderConfig", "import_statement": "import { media.AudioRecorderConfig } from '@ohos.multimedia.media';", "parent_symbol": "media", "is_nested": true, "full_name": "media.AudioRecorderConfig", "score": 0.7336821579999999, "vector_score": 0.61954594, "text_score": 1.0}]}