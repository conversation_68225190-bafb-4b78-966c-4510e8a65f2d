[{"symbol_name": "HttpRequest", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "<p>Defines an HTTP request task. Before invoking APIs provided by HttpRequest, you must call createHttp() to create an HttpRequestTask object.</p> @interface HttpRequest @syscap SystemCapability.Communication.NetStack @crossplatform @atomicservice @since 11 export", "import_statement": "import { http.HttpRequest } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.HttpRequest", "score": 0.0}, {"symbol_name": "CertificatePinning", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "Certificate pinning option. @interface CertificatePinning @syscap SystemCapability.Communication.NetStack @since 12", "import_statement": "import { http.CertificatePinning } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.CertificatePinning", "score": 0.0}, {"symbol_name": "is", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "This", "import_statement": "import { http.is } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.is", "score": 0.0}]