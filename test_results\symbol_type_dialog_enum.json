[{"symbol_name": "DialogAlignment", "symbol_type": "enum", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "The alignment of dialog, @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { DialogAlignment } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.6474429}, {"symbol_name": "DialogButtonStyle", "symbol_type": "enum", "module_name": "enums", "is_default": false, "is_ets": false, "description": "The Button Style of dialog, @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { DialogButtonStyle } from 'enums';", "parent_symbol": null, "is_nested": false, "score": 0.4834426}, {"symbol_name": "DialogButtonDirection", "symbol_type": "enum", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "The arrangement of buttons in dialog. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { DialogButtonDirection } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.42871624}]