[{"symbol_name": "ButtonConfig", "symbol_type": "class", "module_name": "@hms.core.atomicserviceComponent.atomicserviceUi", "is_default": false, "is_ets": true, "description": "Defines the controller to interact with the button property. @syscap SystemCapability.AtomicserviceComponent.UIComponent @stagemodelonly @atomicservice @since 4.1.0(11) export", "import_statement": "import { functionalButtonComponentManager.ButtonConfig } from '@hms.core.atomicserviceComponent.atomicserviceUi';", "parent_symbol": "functionalButtonComponentManager", "is_nested": true, "full_name": "functionalButtonComponentManager.ButtonConfig", "score": 0.6407091}, {"symbol_name": "FunctionalButtonController", "symbol_type": "class", "module_name": "@hms.core.atomicserviceComponent.atomicserviceUi", "is_default": false, "is_ets": true, "description": "Defines the controller to interact with the button. @syscap SystemCapability.AtomicserviceComponent.UIComponent @stagemodelonly @atomicservice @since 4.1.0(11)", "import_statement": "import { FunctionalButtonController } from '@hms.core.atomicserviceComponent.atomicserviceUi';", "parent_symbol": null, "is_nested": false, "score": 0.6289183}, {"symbol_name": "ButtonStyle", "symbol_type": "class", "module_name": "@hms.core.account.LoginComponent", "is_default": false, "is_ets": true, "description": "Defines the HUAWEI ID login button's style. @syscap SystemCapability.AuthenticationServices.HuaweiID.UIComponent @stagemodelonly @since 5.0.0(12)", "import_statement": "import { ButtonStyle } from '@hms.core.account.LoginComponent';", "parent_symbol": null, "is_nested": false, "score": 0.6237661}]