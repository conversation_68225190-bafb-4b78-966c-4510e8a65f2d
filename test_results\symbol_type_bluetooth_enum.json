[{"symbol_name": "BluetoothState", "symbol_type": "enum", "module_name": "@ohos.bluetooth.access", "is_default": false, "is_ets": false, "description": "The enum of bluetooth state. @enum { number } @syscap SystemCapability.Communication.Bluetooth.Core @atomicservice @since 11", "import_statement": "import { BluetoothState } from '@ohos.bluetooth.access';", "parent_symbol": null, "is_nested": false, "score": 0.63626325}, {"symbol_name": "MajorClass", "symbol_type": "enum", "module_name": "@ohos.bluetooth.constant", "is_default": false, "is_ets": false, "description": "The enum of major class of a bluetooth device. @enum { number } @syscap SystemCapability.Communication.Bluetooth.Core @since 10", "import_statement": "import { MajorClass } from '@ohos.bluetooth.constant';", "parent_symbol": null, "is_nested": false, "score": 0.6012309}, {"symbol_name": "MajorMinorClass", "symbol_type": "enum", "module_name": "@ohos.bluetooth.constant", "is_default": false, "is_ets": false, "description": "The enum of major minor class of a bluetooth device. @enum { number } @syscap SystemCapability.Communication.Bluetooth.Core @since 10", "import_statement": "import { MajorMinorClass } from '@ohos.bluetooth.constant';", "parent_symbol": null, "is_nested": false, "score": 0.57787776}]