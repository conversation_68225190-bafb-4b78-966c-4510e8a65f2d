[{"symbol_name": "ATTRS", "symbol_type": "enum", "module_name": "html", "is_default": false, "is_ets": false, "description": "All valid namespaces in HTML. export declare enum NS { HTML = \"http://www.w3.org/1999/xhtml\", MATHML = \"http://www.w3.org/1998/Math/MathML\", SVG = \"http://www.w3.org/2000/svg\", XLINK = \"http://www.w3.org/1999/xlink\", XML = \"http://www.w3.org/XML/1998/namespace\", XMLNS = \"http://www.w3.org/2000/xmlns/\" } export", "import_statement": "import { ATTRS } from 'html';", "parent_symbol": null, "is_nested": false, "score": 0.56737787}, {"symbol_name": "y", "symbol_type": "enum", "module_name": "bt709", "is_default": false, "is_ets": false, "description": "sRGB (based on ITU-R Recommendation BT.709) http://en.wikipedia.org/wiki/SRGB export declare enum Y { RED = 0.2126, GREEN = 0.7152, BLUE = 0.0722, WHITE = 1 } export declare enum x { RED = 0.64, GREEN = 0.3, BLUE = 0.15, WHITE = 0.3127 } export", "import_statement": "import { y } from 'bt709';", "parent_symbol": null, "is_nested": false, "score": 0.5092969}, {"symbol_name": "x", "symbol_type": "enum", "module_name": "bt709", "is_default": false, "is_ets": false, "description": "sRGB (based on ITU-R Recommendation BT.709) http://en.wikipedia.org/wiki/SRGB export declare enum Y { RED = 0.2126, GREEN = 0.7152, BLUE = 0.0722, WHITE = 1 } export", "import_statement": "import { x } from 'bt709';", "parent_symbol": null, "is_nested": false, "score": 0.4554714}]