[{"symbol_name": "ServerHttp2Session", "symbol_type": "interface", "module_name": "http2", "is_default": false, "is_ets": false, "description": "For HTTP/2 Client `Http2Session` instances only, the `http2session.request()`creates and returns an `Http2Stream` instance that can be used to send an HTTP/2 request to the connected server. When a `ClientHttp2Session` is first created, the socket may not yet be connected. if `clienthttp2session.request()` is called during this time, the actual request will be deferred until the socket is ready to go. If the `session` is closed before the actual request be executed, an`ERR_HTTP2_GOAWAY_SESSION` is thrown. This method is only available if `http2session.type` is equal to`http2.constants.NGHTTP2_SESSION_CLIENT`. ```js const http2 = require('node:http2'); const clientSession = http2.connect('https://localhost:1234'); const { HTTP2_HEADER_PATH, HTTP2_HEADER_STATUS, } = http2.constants; const req = clientSession.request({ [HTTP2_HEADER_PATH]: '/' }); req.on('response', (headers) => { console.log(headers[HTTP2_HEADER_STATUS]); req.on('data', (chunk) => { // ..  }); req.on('end', () => { // ..  }); }); ``` When the `options.waitForTrailers` option is set, the `'wantTrailers'` event is emitted immediately after queuing the last chunk of payload data to be sent. The `http2stream.sendTrailers()` method can then be called to send trailing headers to the peer. When `options.waitForTrailers` is set, the `Http2Stream` will not automatically close when the final `DATA` frame is transmitted. User code must call either`http2stream.sendTrailers()` or `http2stream.close()` to close the`Http2Stream`. When `options.signal` is set with an `AbortSignal` and then `abort` on the corresponding `AbortController` is called, the request will emit an `'error'`event with an `AbortError` error. The `:method` and `:path` pseudo-headers are not specified within `headers`, they respectively default to: `:method` \\= `'GET'` `:path` \\= `/` @since v8.4.0 request(headers?: OutgoingHttpHeaders, options?: ClientSessionRequestOptions): ClientHttp2Stream; addListener(event: 'altsvc', listener: (alt: string, origin: string, stream: number) => void): this; addListener(event: 'origin', listener: (origins: string[]) => void): this; addListener(event: 'connect', listener: (session: ClientHttp2Session, socket: net.Socket | tls.TLSSocket) => void): this; addListener(event: 'stream', listener: (stream: ClientHttp2Stream, headers: IncomingHttpHeaders & IncomingHttpStatusHeader, flags: number) => void): this; addListener(event: string | symbol, listener: (...args: any[]) => void): this; emit(event: 'altsvc', alt: string, origin: string, stream: number): boolean; emit(event: 'origin', origins: ReadonlyArray<string>): boolean; emit(event: 'connect', session: ClientHttp2Session, socket: net.Socket | tls.TLSSocket): boolean; emit(event: 'stream', stream: ClientHttp2Stream, headers: IncomingHttpHeaders & IncomingHttpStatusHeader, flags: number): boolean; emit(event: string | symbol, ...args: any[]): boolean; on(event: 'altsvc', listener: (alt: string, origin: string, stream: number) => void): this; on(event: 'origin', listener: (origins: string[]) => void): this; on(event: 'connect', listener: (session: ClientHttp2Session, socket: net.Socket | tls.TLSSocket) => void): this; on(event: 'stream', listener: (stream: ClientHttp2Stream, headers: IncomingHttpHeaders & IncomingHttpStatusHeader, flags: number) => void): this; on(event: string | symbol, listener: (...args: any[]) => void): this; once(event: 'altsvc', listener: (alt: string, origin: string, stream: number) => void): this; once(event: 'origin', listener: (origins: string[]) => void): this; once(event: 'connect', listener: (session: ClientHttp2Session, socket: net.Socket | tls.TLSSocket) => void): this; once(event: 'stream', listener: (stream: ClientHttp2Stream, headers: IncomingHttpHeaders & IncomingHttpStatusHeader, flags: number) => void): this; once(event: string | symbol, listener: (...args: any[]) => void): this; prependListener(event: 'altsvc', listener: (alt: string, origin: string, stream: number) => void): this; prependListener(event: 'origin', listener: (origins: string[]) => void): this; prependListener(event: 'connect', listener: (session: ClientHttp2Session, socket: net.Socket | tls.TLSSocket) => void): this; prependListener(event: 'stream', listener: (stream: ClientHttp2Stream, headers: IncomingHttpHeaders & IncomingHttpStatusHeader, flags: number) => void): this; prependListener(event: string | symbol, listener: (...args: any[]) => void): this; prependOnceListener(event: 'altsvc', listener: (alt: string, origin: string, stream: number) => void): this; prependOnceListener(event: 'origin', listener: (origins: string[]) => void): this; prependOnceListener(event: 'connect', listener: (session: ClientHttp2Session, socket: net.Socket | tls.TLSSocket) => void): this; prependOnceListener(event: 'stream', listener: (stream: ClientHttp2Stream, headers: IncomingHttpHeaders & IncomingHttpStatusHeader, flags: number) => void): this; prependOnceListener(event: string | symbol, listener: (...args: any[]) => void): this; } export interface AlternativeServiceOptions { origin: number | string | url.URL; }", "import_statement": "import { ServerHttp2Session } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.6841024}, {"symbol_name": "ServerStreamFileResponseOptionsWithError", "symbol_type": "interface", "module_name": "http2", "is_default": false, "is_ets": false, "description": "The `node:http2` module provides an implementation of the [HTTP/2](https://tools.ietf.org/html/rfc7540) protocol. It can be accessed using: ```js const http2 = require('node:http2'); ``` @since v8.4.0 @see [source](https://github.com/nodejs/node/blob/v20.2.0/lib/http2.js) declare module 'http2' { import EventEmitter = require('node:events'); import  as fs from 'node:fs'; import  as net from 'node:net'; import  as stream from 'node:stream'; import  as tls from 'node:tls'; import  as url from 'node:url'; import { IncomingHttpHeaders as Http1IncomingHttpHeaders, OutgoingHttpHeaders, IncomingMessage, ServerResponse } from 'node:http'; export { OutgoingHttpHeaders } from 'node:http'; export interface IncomingHttpStatusHeader { ':status'?: number | undefined; } export interface IncomingHttpHeaders extends Http1IncomingHttpHeaders { ':path'?: string | undefined; ':method'?: string | undefined; ':authority'?: string | undefined; ':scheme'?: string | undefined; } // Http2Stream export interface StreamPriorityOptions { exclusive?: boolean | undefined; parent?: number | undefined; weight?: number | undefined; silent?: boolean | undefined; } export interface StreamState { localWindowSize?: number | undefined; state?: number | undefined; localClose?: number | undefined; remoteClose?: number | undefined; sumDependencyWeight?: number | undefined; weight?: number | undefined; } export interface ServerStreamResponseOptions { endStream?: boolean | undefined; waitForTrailers?: boolean | undefined; } export interface StatOptions { offset: number; length: number; } export interface ServerStreamFileResponseOptions { statCheck?(stats: fs.Stats, headers: OutgoingHttpHeaders, statOptions: StatOptions): void | boolean; waitForTrailers?: boolean | undefined; offset?: number | undefined; length?: number | undefined; }", "import_statement": "import { ServerStreamFileResponseOptionsWithError } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.6816443}, {"symbol_name": "Http2Stream", "symbol_type": "interface", "module_name": "http2", "is_default": false, "is_ets": false, "description": "The `node:http2` module provides an implementation of the [HTTP/2](https://tools.ietf.org/html/rfc7540) protocol. It can be accessed using: ```js const http2 = require('node:http2'); ``` @since v8.4.0 @see [source](https://github.com/nodejs/node/blob/v20.2.0/lib/http2.js) declare module 'http2' { import EventEmitter = require('node:events'); import  as fs from 'node:fs'; import  as net from 'node:net'; import  as stream from 'node:stream'; import  as tls from 'node:tls'; import  as url from 'node:url'; import { IncomingHttpHeaders as Http1IncomingHttpHeaders, OutgoingHttpHeaders, IncomingMessage, ServerResponse } from 'node:http'; export { OutgoingHttpHeaders } from 'node:http'; export interface IncomingHttpStatusHeader { ':status'?: number | undefined; } export interface IncomingHttpHeaders extends Http1IncomingHttpHeaders { ':path'?: string | undefined; ':method'?: string | undefined; ':authority'?: string | undefined; ':scheme'?: string | undefined; } // Http2Stream export interface StreamPriorityOptions { exclusive?: boolean | undefined; parent?: number | undefined; weight?: number | undefined; silent?: boolean | undefined; } export interface StreamState { localWindowSize?: number | undefined; state?: number | undefined; localClose?: number | undefined; remoteClose?: number | undefined; sumDependencyWeight?: number | undefined; weight?: number | undefined; } export interface ServerStreamResponseOptions { endStream?: boolean | undefined; waitForTrailers?: boolean | undefined; } export interface StatOptions { offset: number; length: number; } export interface ServerStreamFileResponseOptions { statCheck?(stats: fs.Stats, headers: OutgoingHttpHeaders, statOptions: StatOptions): void | boolean; waitForTrailers?: boolean | undefined; offset?: number | undefined; length?: number | undefined; } export interface ServerStreamFileResponseOptionsWithError extends ServerStreamFileResponseOptions { onError?(err: NodeJS.ErrnoException): void; }", "import_statement": "import { Http2Stream } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.68048817}]