[{"symbol_name": "<PERSON><PERSON>", "symbol_type": "component", "module_name": "button", "is_default": false, "is_ets": false, "description": "UI Component: Defines Button Component. @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 11", "import_statement": "import { But<PERSON> } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.6252831}, {"symbol_name": "FunctionalButton", "symbol_type": "component", "module_name": "@hms.core.atomicserviceComponent.atomicserviceUi", "is_default": false, "is_ets": true, "description": "Defines a UI component used to show the FunctionalButton. @struct { FunctionalButton } @syscap SystemCapability.AtomicserviceComponent.UIComponent @stagemodelonly @atomicservice @since 4.1.0(11)", "import_statement": "import { FunctionalButton } from '@hms.core.atomicserviceComponent.atomicserviceUi';", "parent_symbol": null, "is_nested": false, "score": 0.5706531}, {"symbol_name": "ProgressButton", "symbol_type": "component", "module_name": "@ohos.arkui.advanced.ProgressButton", "is_default": false, "is_ets": true, "description": "Declare Component ProgressButton @syscap SystemCapability.ArkUI.ArkUI.Full @atomicservice @since 11", "import_statement": "import { ProgressButton } from '@ohos.arkui.advanced.ProgressButton';", "parent_symbol": null, "is_nested": false, "score": 0.46256813}]