{"standard": [{"symbol_name": "<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.prompt", "is_default": false, "is_ets": false, "description": "@interface Button @syscap SystemCapability.ArkUI.ArkUI.Full @since 8 @deprecated since 9", "import_statement": "import { prompt.Button } from '@ohos.prompt';", "parent_symbol": "prompt", "is_nested": true, "full_name": "prompt.<PERSON><PERSON>", "score": 0.7461946}, {"symbol_name": "<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.promptAction", "is_default": false, "is_ets": false, "description": "@typedef <PERSON><PERSON> @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { promptAction.Button } from '@ohos.promptAction';", "parent_symbol": "promptAction", "is_nested": true, "full_name": "promptAction.<PERSON><PERSON>", "score": 0.72102755}, {"symbol_name": "<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@system.prompt", "is_default": false, "is_ets": false, "description": "Defines the prompt info of button. @interface But<PERSON> @syscap SystemCapability.ArkUI.ArkUI.Full @atomicservice @since 11", "import_statement": "import { Button } from '@system.prompt';", "parent_symbol": null, "is_nested": false, "score": 0.69622564}], "hybrid": [{"symbol_name": "<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@system.prompt", "is_default": false, "is_ets": false, "description": "Defines the prompt info of button. @interface But<PERSON> @syscap SystemCapability.ArkUI.ArkUI.Full @atomicservice @since 11", "import_statement": "import { Button } from '@system.prompt';", "parent_symbol": null, "is_nested": false, "score": 0.69622564}, {"symbol_name": "ButtonElement", "symbol_type": "interface", "module_name": "js.viewmodel", "is_default": false, "is_ets": false, "description": "The <button> component includes capsule, circle, text, arc, and download buttons. @interface ButtonElement @syscap SystemCapability.ArkUI.ArkUI.Full @since 4", "import_statement": "import { ButtonElement } from 'js.viewmodel';", "parent_symbol": null, "is_nested": false, "score": 0.6844307}, {"symbol_name": "ButtonType", "symbol_type": "enum", "module_name": "button", "is_default": false, "is_ets": false, "description": "Provides a button component. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 11", "import_statement": "import { ButtonType } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.6681042}]}