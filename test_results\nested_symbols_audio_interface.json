[{"symbol_name": "CaptureFilterOptions", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describe playback capture filtering options @typedef CaptureFilterOptions @syscap SystemCapability.Multimedia.Audio.PlaybackCapture @since 10 @deprecated since 12 @useinstead OH_AVScreenCapture in native interface.", "import_statement": "import { audio.CaptureFilterOptions } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.CaptureFilterOptions", "score": 0.0}, {"symbol_name": "AudioCapturerInfo", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describes audio capturer information. @typedef AudioCapturerInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12", "import_statement": "import { audio.AudioCapturerInfo } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioCapturerInfo", "score": 0.0}, {"symbol_name": "AudioDeviceDescriptor", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describes an audio device. @typedef AudioDeviceDescriptor @syscap SystemCapability.Multimedia.Audio.Device @crossplatform @atomicservice @since 12", "import_statement": "import { audio.AudioDeviceDescriptor } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioDeviceDescriptor", "score": 0.0}]