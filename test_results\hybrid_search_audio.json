{"standard": [{"symbol_name": "FileExtension", "symbol_type": "union_type", "module_name": "core", "is_default": false, "is_ets": false, "description": "Union type: | 'jpg'\n\t\t| 'png'\n\t\t| 'apng'\n\t\t| 'gif'\n\t\t| 'webp'\n\t\t| 'flif'\n\t\t| 'xcf'\n\t\t| 'cr2'\n\t\t| 'cr3'\n\t\t| 'orf'\n\t\t| 'arw'\n\t\t| 'dng'\n\t\t| 'nef'\n\t\t| 'rw2'\n\t\t| 'raf'\n\t\t| 'tif'\n\t\t| 'bmp'\n\t\t| 'icns'\n\t\t| 'jxr'\n\t\t| 'psd'\n\t\t| 'indd'\n\t\t| 'zip'\n\t\t| 'tar'\n\t\t| 'rar'\n\t\t| 'gz'\n\t\t| 'bz2'\n\t\t| '7z'\n\t\t| 'dmg'\n\t\t| 'mp4'\n\t\t| 'mid'\n\t\t| 'mkv'\n\t\t| 'webm'\n\t\t| 'mov'\n\t\t| 'avi'\n\t\t| 'mpg'\n\t\t| 'mp2'\n\t\t| 'mp3'\n\t\t| 'm4a'\n\t\t| 'ogg'\n\t\t| 'opus'\n\t\t| 'flac'\n\t\t| 'wav'\n\t\t| 'qcp'\n\t\t| 'amr'\n\t\t| 'pdf'\n\t\t| 'epub'\n\t\t| 'mobi'\n\t\t| 'exe'\n\t\t| 'swf'\n\t\t| 'rtf'\n\t\t| 'woff'\n\t\t| 'woff2'\n\t\t| 'eot'\n\t\t| 'ttf'\n\t\t| 'otf'\n\t\t| 'ico'\n\t\t| 'flv'\n\t\t| 'ps'\n\t\t| 'xz'\n\t\t| 'sqlite'\n\t\t| 'nes'\n\t\t| 'crx'\n\t\t| 'xpi'\n\t\t| 'cab'\n\t\t| 'deb'\n\t\t| 'ar'\n\t\t| 'rpm'\n\t\t| 'Z'\n\t\t| 'lz'\n\t\t| 'cfb'\n\t\t| 'mxf'\n\t\t| 'mts'\n\t\t| 'wasm'\n\t\t| 'blend'\n\t\t| 'bpg'\n\t\t| 'docx'\n\t\t| 'pptx'\n\t\t| 'xlsx'\n\t\t| '3gp'\n\t\t| '3g2'\n\t\t| 'jp2'\n\t\t| 'jpm'\n\t\t| 'jpx'\n\t\t| 'mj2'\n\t\t| 'aif'\n\t\t| 'odt'\n\t\t| 'ods'\n\t\t| 'odp'\n\t\t| 'xml'\n\t\t| 'heic'\n\t\t| 'cur'\n\t\t| 'ktx'\n\t\t| 'ape'\n\t\t| 'wv'\n\t\t| 'asf'\n\t\t| 'dcm'\n\t\t| 'mpc'\n\t\t| 'ics'\n\t\t| 'glb'\n\t\t| 'pcap'\n\t\t| 'dsf'\n\t\t| 'lnk'\n\t\t| 'alias'\n\t\t| 'voc'\n\t\t| 'ac3'\n\t\t| 'm4b'\n\t\t| 'm4p'\n\t\t| 'm4v'\n\t\t| 'f4a'\n\t\t| 'f4b'\n\t\t| 'f4p'\n\t\t| 'f4v'\n\t\t| 'mie'\n\t\t| 'ogv'\n\t\t| 'ogm'\n\t\t| 'oga'\n\t\t| 'spx'\n\t\t| 'ogx'\n\t\t| 'arrow'\n\t\t| 'shp'\n\t\t| 'aac'\n\t\t| 'mp1'\n\t\t| 'it'\n\t\t| 's3m'\n\t\t| 'xm'\n\t\t| 'ai'\n\t\t| 'skp'\n\t\t| 'avif'\n\t\t| 'eps'\n\t\t| 'lzh'\n\t\t| 'pgp'\n\t\t| 'asar'\n\t\t| 'stl'\n\t\t| 'chm'\n\t\t| '3mf'\n\t\t| 'zst'\n\t\t| 'jxl'\n\t\t| 'vcf'", "import_statement": "import type { FileExtension } from 'core';", "parent_symbol": null, "is_nested": false, "score": 0.67811686}, {"symbol_name": "Audio", "symbol_type": "class", "module_name": "@ohos.data.unifiedDataChannel", "is_default": false, "is_ets": false, "description": "Describe the unified audio data @extends File @syscap SystemCapability.DistributedDataManager.UDMF.Core @atomicservice @since 11", "import_statement": "import { unifiedDataChannel.Audio } from '@ohos.data.unifiedDataChannel';", "parent_symbol": "unifiedDataChannel", "is_nested": true, "full_name": "unifiedDataChannel.Audio", "score": 0.67479736}, {"symbol_name": "AudioContextLatencyCategory", "symbol_type": "union_type", "module_name": "lib.dom", "is_default": false, "is_ets": false, "description": "Union type: \"balanced\" | \"interactive\" | \"playback\"", "import_statement": "import type { AudioContextLatencyCategory } from 'lib.dom';", "parent_symbol": null, "is_nested": false, "score": 0.65892524}], "hybrid": [{"symbol_name": "Audio", "symbol_type": "class", "module_name": "@ohos.data.unifiedDataChannel", "is_default": false, "is_ets": false, "description": "Describe the unified audio data @extends File @syscap SystemCapability.DistributedDataManager.UDMF.Core @atomicservice @since 11", "import_statement": "import { unifiedDataChannel.Audio } from '@ohos.data.unifiedDataChannel';", "parent_symbol": "unifiedDataChannel", "is_nested": true, "full_name": "unifiedDataChannel.Audio", "score": 0.67479736}, {"symbol_name": "audio", "symbol_type": "export_assignment", "module_name": "@ohos.multimedia.audio", "is_default": true, "is_ets": false, "description": "Export assignment", "import_statement": "import audio from '@ohos.multimedia.audio';", "parent_symbol": null, "is_nested": false, "score": 0.64458895}, {"symbol_name": "AudioPlayer", "symbol_type": "interface", "module_name": "@ohos.multimedia.media", "is_default": false, "is_ets": false, "description": "Manages and plays audio. Before calling an AudioPlayer method, you must use createAudioPlayer() to create an AudioPlayer instance. @typedef AudioPlayer @syscap SystemCapability.Multimedia.Media.AudioPlayer @since 6 @deprecated since 9 @useinstead ohos.multimedia.media/media.AVPlayer", "import_statement": "import { media.AudioPlayer } from '@ohos.multimedia.media';", "parent_symbol": "media", "is_nested": true, "full_name": "media.AudioPlayer", "score": 0.62260807}]}