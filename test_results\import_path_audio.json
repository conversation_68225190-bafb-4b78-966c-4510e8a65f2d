[{"symbol_name": "audio", "symbol_type": "export_assignment", "module_name": "@ohos.multimedia.audio", "is_default": true, "is_ets": false, "description": "Export assignment", "import_statement": "import audio from '@ohos.multimedia.audio';", "parent_symbol": null, "is_nested": false, "score": 0.64458895}, {"symbol_name": "audioHaptic", "symbol_type": "export_assignment", "module_name": "@ohos.multimedia.audioHaptic", "is_default": true, "is_ets": false, "description": "Export assignment", "import_statement": "import audioHaptic from '@ohos.multimedia.audioHaptic';", "parent_symbol": null, "is_nested": false, "score": 0.58619577}, {"symbol_name": "audioTrackUrl", "symbol_type": "const", "module_name": "core", "is_default": false, "is_ets": false, "description": "Detect the file type from an [`ITokenizer`](https://github.com/Borewit/strtok3#tokenizer) source. This method is used internally, but can also be used for a special \"tokenizer\" reader. A tokenizer propagates the internal read functions, allowing alternative transport mechanisms, to access files, to be implemented and used. An example is [`@tokenizer/http`](https://github.com/Borewit/tokenizer-http), which requests data using [HTTP-range-requests](https://developer.mozilla.org/en-US/docs/Web/HTTP/Range_requests). A difference with a conventional stream and the [tokenizer](https://github.com/Borewit/strtok3#tokenizer), is that it is able to ignore (seek, fast-forward) in the stream. For example, you may only need and read the first 6 bytes, and the last 128 bytes, which may be an advantage in case reading the entire file would take longer. ``` import {makeTokenizer} = require('@tokenizer/http'); import FileType = require('file-type');", "import_statement": "import { audioTrackUrl } from 'core';", "parent_symbol": null, "is_nested": false, "score": 0.48359185}]