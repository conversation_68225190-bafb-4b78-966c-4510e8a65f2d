{"standard": [{"symbol_name": "PerformanceTiming", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "Counting the time taken of various stages of HTTP request. @interface PerformanceTiming @syscap SystemCapability.Communication.NetStack @crossplatform @since 12 export", "import_statement": "import { http.PerformanceTiming } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.PerformanceTiming", "score": 0.75411135}, {"symbol_name": "DataReceiveProgressInfo", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "This interface is used to obtain the progress information of file upload or download. @interface DataReceiveProgressInfo @syscap SystemCapability.Communication.NetStack @crossplatform @since 12 export", "import_statement": "import { http.DataReceiveProgressInfo } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.DataReceiveProgressInfo", "score": 0.75411135}, {"symbol_name": "IDirent", "symbol_type": "interface", "module_name": "types", "is_default": false, "is_ets": false, "description": "Apply the plugin apply(compiler: Compiler): void; }", "import_statement": "import { IDirent } from 'types';", "parent_symbol": null, "is_nested": false, "score": 0.73532665}], "hybrid": [{"symbol_name": "HTTP_STATUS_PAYLOAD_TOO_LARGE", "symbol_type": "const", "module_name": "http2", "is_default": false, "is_ets": false, "description": "Call `http2stream.pushStream()` with the given headers, and wrap the given `Http2Stream` on a newly created `Http2ServerResponse` as the callback parameter if successful. When `Http2ServerRequest` is closed, the callback is called with an error `ERR_HTTP2_INVALID_STREAM`. @since v8.4.0 @param headers An object describing the headers @param callback Called once `http2stream.pushStream()` is finished, or either when the attempt to create the pushed `Http2Stream` has failed or has been rejected, or the state of `Http2ServerRequest` is closed prior to calling the `http2stream.pushStream()` method createPushResponse(headers: OutgoingHttpHeaders, callback: (err: Error | null, res: Http2ServerResponse) => void): void; addListener(event: 'close', listener: () => void): this; addListener(event: 'drain', listener: () => void): this; addListener(event: 'error', listener: (error: Error) => void): this; addListener(event: 'finish', listener: () => void): this; addListener(event: 'pipe', listener: (src: stream.Readable) => void): this; addListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; addListener(event: string | symbol, listener: (...args: any[]) => void): this; emit(event: 'close'): boolean; emit(event: 'drain'): boolean; emit(event: 'error', error: Error): boolean; emit(event: 'finish'): boolean; emit(event: 'pipe', src: stream.Readable): boolean; emit(event: 'unpipe', src: stream.Readable): boolean; emit(event: string | symbol, ...args: any[]): boolean; on(event: 'close', listener: () => void): this; on(event: 'drain', listener: () => void): this; on(event: 'error', listener: (error: Error) => void): this; on(event: 'finish', listener: () => void): this; on(event: 'pipe', listener: (src: stream.Readable) => void): this; on(event: 'unpipe', listener: (src: stream.Readable) => void): this; on(event: string | symbol, listener: (...args: any[]) => void): this; once(event: 'close', listener: () => void): this; once(event: 'drain', listener: () => void): this; once(event: 'error', listener: (error: Error) => void): this; once(event: 'finish', listener: () => void): this; once(event: 'pipe', listener: (src: stream.Readable) => void): this; once(event: 'unpipe', listener: (src: stream.Readable) => void): this; once(event: string | symbol, listener: (...args: any[]) => void): this; prependListener(event: 'close', listener: () => void): this; prependListener(event: 'drain', listener: () => void): this; prependListener(event: 'error', listener: (error: Error) => void): this; prependListener(event: 'finish', listener: () => void): this; prependListener(event: 'pipe', listener: (src: stream.Readable) => void): this; prependListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; prependListener(event: string | symbol, listener: (...args: any[]) => void): this; prependOnceListener(event: 'close', listener: () => void): this; prependOnceListener(event: 'drain', listener: () => void): this; prependOnceListener(event: 'error', listener: (error: Error) => void): this; prependOnceListener(event: 'finish', listener: () => void): this; prependOnceListener(event: 'pipe', listener: (src: stream.Readable) => void): this; prependOnceListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; prependOnceListener(event: string | symbol, listener: (...args: any[]) => void): this; } export namespace constants { const NGHTTP2_SESSION_SERVER: number; const NGHTTP2_SESSION_CLIENT: number; const NGHTTP2_STREAM_STATE_IDLE: number; const NGHTTP2_STREAM_STATE_OPEN: number; const NGHTTP2_STREAM_STATE_RESERVED_LOCAL: number; const NGHTTP2_STREAM_STATE_RESERVED_REMOTE: number; const NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL: number; const NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE: number; const NGHTTP2_STREAM_STATE_CLOSED: number; const NGHTTP2_NO_ERROR: number; const NGHTTP2_PROTOCOL_ERROR: number; const NGHTTP2_INTERNAL_ERROR: number; const NGHTTP2_FLOW_CONTROL_ERROR: number; const NGHTTP2_SETTINGS_TIMEOUT: number; const NGHTTP2_STREAM_CLOSED: number; const NGHTTP2_FRAME_SIZE_ERROR: number; const NGHTTP2_REFUSED_STREAM: number; const NGHTTP2_CANCEL: number; const NGHTTP2_COMPRESSION_ERROR: number; const NGHTTP2_CONNECT_ERROR: number; const NGHTTP2_ENHANCE_YOUR_CALM: number; const NGHTTP2_INADEQUATE_SECURITY: number; const NGHTTP2_HTTP_1_1_REQUIRED: number; const NGHTTP2_ERR_FRAME_SIZE_ERROR: number; const NGHTTP2_FLAG_NONE: number; const NGHTTP2_FLAG_END_STREAM: number; const NGHTTP2_FLAG_END_HEADERS: number; const NGHTTP2_FLAG_ACK: number; const NGHTTP2_FLAG_PADDED: number; const NGHTTP2_FLAG_PRIORITY: number; const DEFAULT_SETTINGS_HEADER_TABLE_SIZE: number; const DEFAULT_SETTINGS_ENABLE_PUSH: number; const DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE: number; const DEFAULT_SETTINGS_MAX_FRAME_SIZE: number; const MAX_MAX_FRAME_SIZE: number; const MIN_MAX_FRAME_SIZE: number; const MAX_INITIAL_WINDOW_SIZE: number; const NGHTTP2_DEFAULT_WEIGHT: number; const NGHTTP2_SETTINGS_HEADER_TABLE_SIZE: number; const NGHTTP2_SETTINGS_ENABLE_PUSH: number; const NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS: number; const NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE: number; const NGHTTP2_SETTINGS_MAX_FRAME_SIZE: number; const NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE: number; const PADDING_STRATEGY_NONE: number; const PADDING_STRATEGY_MAX: number; const PADDING_STRATEGY_CALLBACK: number; const HTTP2_HEADER_STATUS: string; const HTTP2_HEADER_METHOD: string; const HTTP2_HEADER_AUTHORITY: string; const HTTP2_HEADER_SCHEME: string; const HTTP2_HEADER_PATH: string; const HTTP2_HEADER_ACCEPT_CHARSET: string; const HTTP2_HEADER_ACCEPT_ENCODING: string; const HTTP2_HEADER_ACCEPT_LANGUAGE: string; const HTTP2_HEADER_ACCEPT_RANGES: string; const HTTP2_HEADER_ACCEPT: string; const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN: string; const HTTP2_HEADER_AGE: string; const HTTP2_HEADER_ALLOW: string; const HTTP2_HEADER_AUTHORIZATION: string; const HTTP2_HEADER_CACHE_CONTROL: string; const HTTP2_HEADER_CONNECTION: string; const HTTP2_HEADER_CONTENT_DISPOSITION: string; const HTTP2_HEADER_CONTENT_ENCODING: string; const HTTP2_HEADER_CONTENT_LANGUAGE: string; const HTTP2_HEADER_CONTENT_LENGTH: string; const HTTP2_HEADER_CONTENT_LOCATION: string; const HTTP2_HEADER_CONTENT_MD5: string; const HTTP2_HEADER_CONTENT_RANGE: string; const HTTP2_HEADER_CONTENT_TYPE: string; const HTTP2_HEADER_COOKIE: string; const HTTP2_HEADER_DATE: string; const HTTP2_HEADER_ETAG: string; const HTTP2_HEADER_EXPECT: string; const HTTP2_HEADER_EXPIRES: string; const HTTP2_HEADER_FROM: string; const HTTP2_HEADER_HOST: string; const HTTP2_HEADER_IF_MATCH: string; const HTTP2_HEADER_IF_MODIFIED_SINCE: string; const HTTP2_HEADER_IF_NONE_MATCH: string; const HTTP2_HEADER_IF_RANGE: string; const HTTP2_HEADER_IF_UNMODIFIED_SINCE: string; const HTTP2_HEADER_LAST_MODIFIED: string; const HTTP2_HEADER_LINK: string; const HTTP2_HEADER_LOCATION: string; const HTTP2_HEADER_MAX_FORWARDS: string; const HTTP2_HEADER_PREFER: string; const HTTP2_HEADER_PROXY_AUTHENTICATE: string; const HTTP2_HEADER_PROXY_AUTHORIZATION: string; const HTTP2_HEADER_RANGE: string; const HTTP2_HEADER_REFERER: string; const HTTP2_HEADER_REFRESH: string; const HTTP2_HEADER_RETRY_AFTER: string; const HTTP2_HEADER_SERVER: string; const HTTP2_HEADER_SET_COOKIE: string; const HTTP2_HEADER_STRICT_TRANSPORT_SECURITY: string; const HTTP2_HEADER_TRANSFER_ENCODING: string; const HTTP2_HEADER_TE: string; const HTTP2_HEADER_UPGRADE: string; const HTTP2_HEADER_USER_AGENT: string; const HTTP2_HEADER_VARY: string; const HTTP2_HEADER_VIA: string; const HTTP2_HEADER_WWW_AUTHENTICATE: string; const HTTP2_HEADER_HTTP2_SETTINGS: string; const HTTP2_HEADER_KEEP_ALIVE: string; const HTTP2_HEADER_PROXY_CONNECTION: string; const HTTP2_METHOD_ACL: string; const HTTP2_METHOD_BASELINE_CONTROL: string; const HTTP2_METHOD_BIND: string; const HTTP2_METHOD_CHECKIN: string; const HTTP2_METHOD_CHECKOUT: string; const HTTP2_METHOD_CONNECT: string; const HTTP2_METHOD_COPY: string; const HTTP2_METHOD_DELETE: string; const HTTP2_METHOD_GET: string; const HTTP2_METHOD_HEAD: string; const HTTP2_METHOD_LABEL: string; const HTTP2_METHOD_LINK: string; const HTTP2_METHOD_LOCK: string; const HTTP2_METHOD_MERGE: string; const HTTP2_METHOD_MKACTIVITY: string; const HTTP2_METHOD_MKCALENDAR: string; const HTTP2_METHOD_MKCOL: string; const HTTP2_METHOD_MKREDIRECTREF: string; const HTTP2_METHOD_MKWORKSPACE: string; const HTTP2_METHOD_MOVE: string; const HTTP2_METHOD_OPTIONS: string; const HTTP2_METHOD_ORDERPATCH: string; const HTTP2_METHOD_PATCH: string; const HTTP2_METHOD_POST: string; const HTTP2_METHOD_PRI: string; const HTTP2_METHOD_PROPFIND: string; const HTTP2_METHOD_PROPPATCH: string; const HTTP2_METHOD_PUT: string; const HTTP2_METHOD_REBIND: string; const HTTP2_METHOD_REPORT: string; const HTTP2_METHOD_SEARCH: string; const HTTP2_METHOD_TRACE: string; const HTTP2_METHOD_UNBIND: string; const HTTP2_METHOD_UNCHECKOUT: string; const HTTP2_METHOD_UNLINK: string; const HTTP2_METHOD_UNLOCK: string; const HTTP2_METHOD_UPDATE: string; const HTTP2_METHOD_UPDATEREDIRECTREF: string; const HTTP2_METHOD_VERSION_CONTROL: string; const HTTP_STATUS_CONTINUE: number; const HTTP_STATUS_SWITCHING_PROTOCOLS: number; const HTTP_STATUS_PROCESSING: number; const HTTP_STATUS_OK: number; const HTTP_STATUS_CREATED: number; const HTTP_STATUS_ACCEPTED: number; const HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION: number; const HTTP_STATUS_NO_CONTENT: number; const HTTP_STATUS_RESET_CONTENT: number; const HTTP_STATUS_PARTIAL_CONTENT: number; const HTTP_STATUS_MULTI_STATUS: number; const HTTP_STATUS_ALREADY_REPORTED: number; const HTTP_STATUS_IM_USED: number; const HTTP_STATUS_MULTIPLE_CHOICES: number; const HTTP_STATUS_MOVED_PERMANENTLY: number; const HTTP_STATUS_FOUND: number; const HTTP_STATUS_SEE_OTHER: number; const HTTP_STATUS_NOT_MODIFIED: number; const HTTP_STATUS_USE_PROXY: number; const HTTP_STATUS_TEMPORARY_REDIRECT: number; const HTTP_STATUS_PERMANENT_REDIRECT: number; const HTTP_STATUS_BAD_REQUEST: number; const HTTP_STATUS_UNAUTHORIZED: number; const HTTP_STATUS_PAYMENT_REQUIRED: number; const HTTP_STATUS_FORBIDDEN: number; const HTTP_STATUS_NOT_FOUND: number; const HTTP_STATUS_METHOD_NOT_ALLOWED: number; const HTTP_STATUS_NOT_ACCEPTABLE: number; const HTTP_STATUS_PROXY_AUTHENTICATION_REQUIRED: number; const HTTP_STATUS_REQUEST_TIMEOUT: number; const HTTP_STATUS_CONFLICT: number; const HTTP_STATUS_GONE: number; const HTTP_STATUS_LENGTH_REQUIRED: number; const HTTP_STATUS_PRECONDITION_FAILED: number;", "import_statement": "import { HTTP_STATUS_PAYLOAD_TOO_LARGE } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.71523654}, {"symbol_name": "HTTP_STATUS_FOUND", "symbol_type": "const", "module_name": "http2", "is_default": false, "is_ets": false, "description": "Call `http2stream.pushStream()` with the given headers, and wrap the given `Http2Stream` on a newly created `Http2ServerResponse` as the callback parameter if successful. When `Http2ServerRequest` is closed, the callback is called with an error `ERR_HTTP2_INVALID_STREAM`. @since v8.4.0 @param headers An object describing the headers @param callback Called once `http2stream.pushStream()` is finished, or either when the attempt to create the pushed `Http2Stream` has failed or has been rejected, or the state of `Http2ServerRequest` is closed prior to calling the `http2stream.pushStream()` method createPushResponse(headers: OutgoingHttpHeaders, callback: (err: Error | null, res: Http2ServerResponse) => void): void; addListener(event: 'close', listener: () => void): this; addListener(event: 'drain', listener: () => void): this; addListener(event: 'error', listener: (error: Error) => void): this; addListener(event: 'finish', listener: () => void): this; addListener(event: 'pipe', listener: (src: stream.Readable) => void): this; addListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; addListener(event: string | symbol, listener: (...args: any[]) => void): this; emit(event: 'close'): boolean; emit(event: 'drain'): boolean; emit(event: 'error', error: Error): boolean; emit(event: 'finish'): boolean; emit(event: 'pipe', src: stream.Readable): boolean; emit(event: 'unpipe', src: stream.Readable): boolean; emit(event: string | symbol, ...args: any[]): boolean; on(event: 'close', listener: () => void): this; on(event: 'drain', listener: () => void): this; on(event: 'error', listener: (error: Error) => void): this; on(event: 'finish', listener: () => void): this; on(event: 'pipe', listener: (src: stream.Readable) => void): this; on(event: 'unpipe', listener: (src: stream.Readable) => void): this; on(event: string | symbol, listener: (...args: any[]) => void): this; once(event: 'close', listener: () => void): this; once(event: 'drain', listener: () => void): this; once(event: 'error', listener: (error: Error) => void): this; once(event: 'finish', listener: () => void): this; once(event: 'pipe', listener: (src: stream.Readable) => void): this; once(event: 'unpipe', listener: (src: stream.Readable) => void): this; once(event: string | symbol, listener: (...args: any[]) => void): this; prependListener(event: 'close', listener: () => void): this; prependListener(event: 'drain', listener: () => void): this; prependListener(event: 'error', listener: (error: Error) => void): this; prependListener(event: 'finish', listener: () => void): this; prependListener(event: 'pipe', listener: (src: stream.Readable) => void): this; prependListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; prependListener(event: string | symbol, listener: (...args: any[]) => void): this; prependOnceListener(event: 'close', listener: () => void): this; prependOnceListener(event: 'drain', listener: () => void): this; prependOnceListener(event: 'error', listener: (error: Error) => void): this; prependOnceListener(event: 'finish', listener: () => void): this; prependOnceListener(event: 'pipe', listener: (src: stream.Readable) => void): this; prependOnceListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; prependOnceListener(event: string | symbol, listener: (...args: any[]) => void): this; } export namespace constants { const NGHTTP2_SESSION_SERVER: number; const NGHTTP2_SESSION_CLIENT: number; const NGHTTP2_STREAM_STATE_IDLE: number; const NGHTTP2_STREAM_STATE_OPEN: number; const NGHTTP2_STREAM_STATE_RESERVED_LOCAL: number; const NGHTTP2_STREAM_STATE_RESERVED_REMOTE: number; const NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL: number; const NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE: number; const NGHTTP2_STREAM_STATE_CLOSED: number; const NGHTTP2_NO_ERROR: number; const NGHTTP2_PROTOCOL_ERROR: number; const NGHTTP2_INTERNAL_ERROR: number; const NGHTTP2_FLOW_CONTROL_ERROR: number; const NGHTTP2_SETTINGS_TIMEOUT: number; const NGHTTP2_STREAM_CLOSED: number; const NGHTTP2_FRAME_SIZE_ERROR: number; const NGHTTP2_REFUSED_STREAM: number; const NGHTTP2_CANCEL: number; const NGHTTP2_COMPRESSION_ERROR: number; const NGHTTP2_CONNECT_ERROR: number; const NGHTTP2_ENHANCE_YOUR_CALM: number; const NGHTTP2_INADEQUATE_SECURITY: number; const NGHTTP2_HTTP_1_1_REQUIRED: number; const NGHTTP2_ERR_FRAME_SIZE_ERROR: number; const NGHTTP2_FLAG_NONE: number; const NGHTTP2_FLAG_END_STREAM: number; const NGHTTP2_FLAG_END_HEADERS: number; const NGHTTP2_FLAG_ACK: number; const NGHTTP2_FLAG_PADDED: number; const NGHTTP2_FLAG_PRIORITY: number; const DEFAULT_SETTINGS_HEADER_TABLE_SIZE: number; const DEFAULT_SETTINGS_ENABLE_PUSH: number; const DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE: number; const DEFAULT_SETTINGS_MAX_FRAME_SIZE: number; const MAX_MAX_FRAME_SIZE: number; const MIN_MAX_FRAME_SIZE: number; const MAX_INITIAL_WINDOW_SIZE: number; const NGHTTP2_DEFAULT_WEIGHT: number; const NGHTTP2_SETTINGS_HEADER_TABLE_SIZE: number; const NGHTTP2_SETTINGS_ENABLE_PUSH: number; const NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS: number; const NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE: number; const NGHTTP2_SETTINGS_MAX_FRAME_SIZE: number; const NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE: number; const PADDING_STRATEGY_NONE: number; const PADDING_STRATEGY_MAX: number; const PADDING_STRATEGY_CALLBACK: number; const HTTP2_HEADER_STATUS: string; const HTTP2_HEADER_METHOD: string; const HTTP2_HEADER_AUTHORITY: string; const HTTP2_HEADER_SCHEME: string; const HTTP2_HEADER_PATH: string; const HTTP2_HEADER_ACCEPT_CHARSET: string; const HTTP2_HEADER_ACCEPT_ENCODING: string; const HTTP2_HEADER_ACCEPT_LANGUAGE: string; const HTTP2_HEADER_ACCEPT_RANGES: string; const HTTP2_HEADER_ACCEPT: string; const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN: string; const HTTP2_HEADER_AGE: string; const HTTP2_HEADER_ALLOW: string; const HTTP2_HEADER_AUTHORIZATION: string; const HTTP2_HEADER_CACHE_CONTROL: string; const HTTP2_HEADER_CONNECTION: string; const HTTP2_HEADER_CONTENT_DISPOSITION: string; const HTTP2_HEADER_CONTENT_ENCODING: string; const HTTP2_HEADER_CONTENT_LANGUAGE: string; const HTTP2_HEADER_CONTENT_LENGTH: string; const HTTP2_HEADER_CONTENT_LOCATION: string; const HTTP2_HEADER_CONTENT_MD5: string; const HTTP2_HEADER_CONTENT_RANGE: string; const HTTP2_HEADER_CONTENT_TYPE: string; const HTTP2_HEADER_COOKIE: string; const HTTP2_HEADER_DATE: string; const HTTP2_HEADER_ETAG: string; const HTTP2_HEADER_EXPECT: string; const HTTP2_HEADER_EXPIRES: string; const HTTP2_HEADER_FROM: string; const HTTP2_HEADER_HOST: string; const HTTP2_HEADER_IF_MATCH: string; const HTTP2_HEADER_IF_MODIFIED_SINCE: string; const HTTP2_HEADER_IF_NONE_MATCH: string; const HTTP2_HEADER_IF_RANGE: string; const HTTP2_HEADER_IF_UNMODIFIED_SINCE: string; const HTTP2_HEADER_LAST_MODIFIED: string; const HTTP2_HEADER_LINK: string; const HTTP2_HEADER_LOCATION: string; const HTTP2_HEADER_MAX_FORWARDS: string; const HTTP2_HEADER_PREFER: string; const HTTP2_HEADER_PROXY_AUTHENTICATE: string; const HTTP2_HEADER_PROXY_AUTHORIZATION: string; const HTTP2_HEADER_RANGE: string; const HTTP2_HEADER_REFERER: string; const HTTP2_HEADER_REFRESH: string; const HTTP2_HEADER_RETRY_AFTER: string; const HTTP2_HEADER_SERVER: string; const HTTP2_HEADER_SET_COOKIE: string; const HTTP2_HEADER_STRICT_TRANSPORT_SECURITY: string; const HTTP2_HEADER_TRANSFER_ENCODING: string; const HTTP2_HEADER_TE: string; const HTTP2_HEADER_UPGRADE: string; const HTTP2_HEADER_USER_AGENT: string; const HTTP2_HEADER_VARY: string; const HTTP2_HEADER_VIA: string; const HTTP2_HEADER_WWW_AUTHENTICATE: string; const HTTP2_HEADER_HTTP2_SETTINGS: string; const HTTP2_HEADER_KEEP_ALIVE: string; const HTTP2_HEADER_PROXY_CONNECTION: string; const HTTP2_METHOD_ACL: string; const HTTP2_METHOD_BASELINE_CONTROL: string; const HTTP2_METHOD_BIND: string; const HTTP2_METHOD_CHECKIN: string; const HTTP2_METHOD_CHECKOUT: string; const HTTP2_METHOD_CONNECT: string; const HTTP2_METHOD_COPY: string; const HTTP2_METHOD_DELETE: string; const HTTP2_METHOD_GET: string; const HTTP2_METHOD_HEAD: string; const HTTP2_METHOD_LABEL: string; const HTTP2_METHOD_LINK: string; const HTTP2_METHOD_LOCK: string; const HTTP2_METHOD_MERGE: string; const HTTP2_METHOD_MKACTIVITY: string; const HTTP2_METHOD_MKCALENDAR: string; const HTTP2_METHOD_MKCOL: string; const HTTP2_METHOD_MKREDIRECTREF: string; const HTTP2_METHOD_MKWORKSPACE: string; const HTTP2_METHOD_MOVE: string; const HTTP2_METHOD_OPTIONS: string; const HTTP2_METHOD_ORDERPATCH: string; const HTTP2_METHOD_PATCH: string; const HTTP2_METHOD_POST: string; const HTTP2_METHOD_PRI: string; const HTTP2_METHOD_PROPFIND: string; const HTTP2_METHOD_PROPPATCH: string; const HTTP2_METHOD_PUT: string; const HTTP2_METHOD_REBIND: string; const HTTP2_METHOD_REPORT: string; const HTTP2_METHOD_SEARCH: string; const HTTP2_METHOD_TRACE: string; const HTTP2_METHOD_UNBIND: string; const HTTP2_METHOD_UNCHECKOUT: string; const HTTP2_METHOD_UNLINK: string; const HTTP2_METHOD_UNLOCK: string; const HTTP2_METHOD_UPDATE: string; const HTTP2_METHOD_UPDATEREDIRECTREF: string; const HTTP2_METHOD_VERSION_CONTROL: string; const HTTP_STATUS_CONTINUE: number; const HTTP_STATUS_SWITCHING_PROTOCOLS: number; const HTTP_STATUS_PROCESSING: number; const HTTP_STATUS_OK: number; const HTTP_STATUS_CREATED: number; const HTTP_STATUS_ACCEPTED: number; const HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION: number; const HTTP_STATUS_NO_CONTENT: number; const HTTP_STATUS_RESET_CONTENT: number; const HTTP_STATUS_PARTIAL_CONTENT: number; const HTTP_STATUS_MULTI_STATUS: number; const HTTP_STATUS_ALREADY_REPORTED: number; const HTTP_STATUS_IM_USED: number; const HTTP_STATUS_MULTIPLE_CHOICES: number; const HTTP_STATUS_MOVED_PERMANENTLY: number;", "import_statement": "import { HTTP_STATUS_FOUND } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.71281666}, {"symbol_name": "HTTP_STATUS_MOVED_PERMANENTLY", "symbol_type": "const", "module_name": "http2", "is_default": false, "is_ets": false, "description": "Call `http2stream.pushStream()` with the given headers, and wrap the given `Http2Stream` on a newly created `Http2ServerResponse` as the callback parameter if successful. When `Http2ServerRequest` is closed, the callback is called with an error `ERR_HTTP2_INVALID_STREAM`. @since v8.4.0 @param headers An object describing the headers @param callback Called once `http2stream.pushStream()` is finished, or either when the attempt to create the pushed `Http2Stream` has failed or has been rejected, or the state of `Http2ServerRequest` is closed prior to calling the `http2stream.pushStream()` method createPushResponse(headers: OutgoingHttpHeaders, callback: (err: Error | null, res: Http2ServerResponse) => void): void; addListener(event: 'close', listener: () => void): this; addListener(event: 'drain', listener: () => void): this; addListener(event: 'error', listener: (error: Error) => void): this; addListener(event: 'finish', listener: () => void): this; addListener(event: 'pipe', listener: (src: stream.Readable) => void): this; addListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; addListener(event: string | symbol, listener: (...args: any[]) => void): this; emit(event: 'close'): boolean; emit(event: 'drain'): boolean; emit(event: 'error', error: Error): boolean; emit(event: 'finish'): boolean; emit(event: 'pipe', src: stream.Readable): boolean; emit(event: 'unpipe', src: stream.Readable): boolean; emit(event: string | symbol, ...args: any[]): boolean; on(event: 'close', listener: () => void): this; on(event: 'drain', listener: () => void): this; on(event: 'error', listener: (error: Error) => void): this; on(event: 'finish', listener: () => void): this; on(event: 'pipe', listener: (src: stream.Readable) => void): this; on(event: 'unpipe', listener: (src: stream.Readable) => void): this; on(event: string | symbol, listener: (...args: any[]) => void): this; once(event: 'close', listener: () => void): this; once(event: 'drain', listener: () => void): this; once(event: 'error', listener: (error: Error) => void): this; once(event: 'finish', listener: () => void): this; once(event: 'pipe', listener: (src: stream.Readable) => void): this; once(event: 'unpipe', listener: (src: stream.Readable) => void): this; once(event: string | symbol, listener: (...args: any[]) => void): this; prependListener(event: 'close', listener: () => void): this; prependListener(event: 'drain', listener: () => void): this; prependListener(event: 'error', listener: (error: Error) => void): this; prependListener(event: 'finish', listener: () => void): this; prependListener(event: 'pipe', listener: (src: stream.Readable) => void): this; prependListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; prependListener(event: string | symbol, listener: (...args: any[]) => void): this; prependOnceListener(event: 'close', listener: () => void): this; prependOnceListener(event: 'drain', listener: () => void): this; prependOnceListener(event: 'error', listener: (error: Error) => void): this; prependOnceListener(event: 'finish', listener: () => void): this; prependOnceListener(event: 'pipe', listener: (src: stream.Readable) => void): this; prependOnceListener(event: 'unpipe', listener: (src: stream.Readable) => void): this; prependOnceListener(event: string | symbol, listener: (...args: any[]) => void): this; } export namespace constants { const NGHTTP2_SESSION_SERVER: number; const NGHTTP2_SESSION_CLIENT: number; const NGHTTP2_STREAM_STATE_IDLE: number; const NGHTTP2_STREAM_STATE_OPEN: number; const NGHTTP2_STREAM_STATE_RESERVED_LOCAL: number; const NGHTTP2_STREAM_STATE_RESERVED_REMOTE: number; const NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL: number; const NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE: number; const NGHTTP2_STREAM_STATE_CLOSED: number; const NGHTTP2_NO_ERROR: number; const NGHTTP2_PROTOCOL_ERROR: number; const NGHTTP2_INTERNAL_ERROR: number; const NGHTTP2_FLOW_CONTROL_ERROR: number; const NGHTTP2_SETTINGS_TIMEOUT: number; const NGHTTP2_STREAM_CLOSED: number; const NGHTTP2_FRAME_SIZE_ERROR: number; const NGHTTP2_REFUSED_STREAM: number; const NGHTTP2_CANCEL: number; const NGHTTP2_COMPRESSION_ERROR: number; const NGHTTP2_CONNECT_ERROR: number; const NGHTTP2_ENHANCE_YOUR_CALM: number; const NGHTTP2_INADEQUATE_SECURITY: number; const NGHTTP2_HTTP_1_1_REQUIRED: number; const NGHTTP2_ERR_FRAME_SIZE_ERROR: number; const NGHTTP2_FLAG_NONE: number; const NGHTTP2_FLAG_END_STREAM: number; const NGHTTP2_FLAG_END_HEADERS: number; const NGHTTP2_FLAG_ACK: number; const NGHTTP2_FLAG_PADDED: number; const NGHTTP2_FLAG_PRIORITY: number; const DEFAULT_SETTINGS_HEADER_TABLE_SIZE: number; const DEFAULT_SETTINGS_ENABLE_PUSH: number; const DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE: number; const DEFAULT_SETTINGS_MAX_FRAME_SIZE: number; const MAX_MAX_FRAME_SIZE: number; const MIN_MAX_FRAME_SIZE: number; const MAX_INITIAL_WINDOW_SIZE: number; const NGHTTP2_DEFAULT_WEIGHT: number; const NGHTTP2_SETTINGS_HEADER_TABLE_SIZE: number; const NGHTTP2_SETTINGS_ENABLE_PUSH: number; const NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS: number; const NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE: number; const NGHTTP2_SETTINGS_MAX_FRAME_SIZE: number; const NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE: number; const PADDING_STRATEGY_NONE: number; const PADDING_STRATEGY_MAX: number; const PADDING_STRATEGY_CALLBACK: number; const HTTP2_HEADER_STATUS: string; const HTTP2_HEADER_METHOD: string; const HTTP2_HEADER_AUTHORITY: string; const HTTP2_HEADER_SCHEME: string; const HTTP2_HEADER_PATH: string; const HTTP2_HEADER_ACCEPT_CHARSET: string; const HTTP2_HEADER_ACCEPT_ENCODING: string; const HTTP2_HEADER_ACCEPT_LANGUAGE: string; const HTTP2_HEADER_ACCEPT_RANGES: string; const HTTP2_HEADER_ACCEPT: string; const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN: string; const HTTP2_HEADER_AGE: string; const HTTP2_HEADER_ALLOW: string; const HTTP2_HEADER_AUTHORIZATION: string; const HTTP2_HEADER_CACHE_CONTROL: string; const HTTP2_HEADER_CONNECTION: string; const HTTP2_HEADER_CONTENT_DISPOSITION: string; const HTTP2_HEADER_CONTENT_ENCODING: string; const HTTP2_HEADER_CONTENT_LANGUAGE: string; const HTTP2_HEADER_CONTENT_LENGTH: string; const HTTP2_HEADER_CONTENT_LOCATION: string; const HTTP2_HEADER_CONTENT_MD5: string; const HTTP2_HEADER_CONTENT_RANGE: string; const HTTP2_HEADER_CONTENT_TYPE: string; const HTTP2_HEADER_COOKIE: string; const HTTP2_HEADER_DATE: string; const HTTP2_HEADER_ETAG: string; const HTTP2_HEADER_EXPECT: string; const HTTP2_HEADER_EXPIRES: string; const HTTP2_HEADER_FROM: string; const HTTP2_HEADER_HOST: string; const HTTP2_HEADER_IF_MATCH: string; const HTTP2_HEADER_IF_MODIFIED_SINCE: string; const HTTP2_HEADER_IF_NONE_MATCH: string; const HTTP2_HEADER_IF_RANGE: string; const HTTP2_HEADER_IF_UNMODIFIED_SINCE: string; const HTTP2_HEADER_LAST_MODIFIED: string; const HTTP2_HEADER_LINK: string; const HTTP2_HEADER_LOCATION: string; const HTTP2_HEADER_MAX_FORWARDS: string; const HTTP2_HEADER_PREFER: string; const HTTP2_HEADER_PROXY_AUTHENTICATE: string; const HTTP2_HEADER_PROXY_AUTHORIZATION: string; const HTTP2_HEADER_RANGE: string; const HTTP2_HEADER_REFERER: string; const HTTP2_HEADER_REFRESH: string; const HTTP2_HEADER_RETRY_AFTER: string; const HTTP2_HEADER_SERVER: string; const HTTP2_HEADER_SET_COOKIE: string; const HTTP2_HEADER_STRICT_TRANSPORT_SECURITY: string; const HTTP2_HEADER_TRANSFER_ENCODING: string; const HTTP2_HEADER_TE: string; const HTTP2_HEADER_UPGRADE: string; const HTTP2_HEADER_USER_AGENT: string; const HTTP2_HEADER_VARY: string; const HTTP2_HEADER_VIA: string; const HTTP2_HEADER_WWW_AUTHENTICATE: string; const HTTP2_HEADER_HTTP2_SETTINGS: string; const HTTP2_HEADER_KEEP_ALIVE: string; const HTTP2_HEADER_PROXY_CONNECTION: string; const HTTP2_METHOD_ACL: string; const HTTP2_METHOD_BASELINE_CONTROL: string; const HTTP2_METHOD_BIND: string; const HTTP2_METHOD_CHECKIN: string; const HTTP2_METHOD_CHECKOUT: string; const HTTP2_METHOD_CONNECT: string; const HTTP2_METHOD_COPY: string; const HTTP2_METHOD_DELETE: string; const HTTP2_METHOD_GET: string; const HTTP2_METHOD_HEAD: string; const HTTP2_METHOD_LABEL: string; const HTTP2_METHOD_LINK: string; const HTTP2_METHOD_LOCK: string; const HTTP2_METHOD_MERGE: string; const HTTP2_METHOD_MKACTIVITY: string; const HTTP2_METHOD_MKCALENDAR: string; const HTTP2_METHOD_MKCOL: string; const HTTP2_METHOD_MKREDIRECTREF: string; const HTTP2_METHOD_MKWORKSPACE: string; const HTTP2_METHOD_MOVE: string; const HTTP2_METHOD_OPTIONS: string; const HTTP2_METHOD_ORDERPATCH: string; const HTTP2_METHOD_PATCH: string; const HTTP2_METHOD_POST: string; const HTTP2_METHOD_PRI: string; const HTTP2_METHOD_PROPFIND: string; const HTTP2_METHOD_PROPPATCH: string; const HTTP2_METHOD_PUT: string; const HTTP2_METHOD_REBIND: string; const HTTP2_METHOD_REPORT: string; const HTTP2_METHOD_SEARCH: string; const HTTP2_METHOD_TRACE: string; const HTTP2_METHOD_UNBIND: string; const HTTP2_METHOD_UNCHECKOUT: string; const HTTP2_METHOD_UNLINK: string; const HTTP2_METHOD_UNLOCK: string; const HTTP2_METHOD_UPDATE: string; const HTTP2_METHOD_UPDATEREDIRECTREF: string; const HTTP2_METHOD_VERSION_CONTROL: string; const HTTP_STATUS_CONTINUE: number; const HTTP_STATUS_SWITCHING_PROTOCOLS: number; const HTTP_STATUS_PROCESSING: number; const HTTP_STATUS_OK: number; const HTTP_STATUS_CREATED: number; const HTTP_STATUS_ACCEPTED: number; const HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION: number; const HTTP_STATUS_NO_CONTENT: number; const HTTP_STATUS_RESET_CONTENT: number; const HTTP_STATUS_PARTIAL_CONTENT: number; const HTTP_STATUS_MULTI_STATUS: number; const HTTP_STATUS_ALREADY_REPORTED: number; const HTTP_STATUS_IM_USED: number; const HTTP_STATUS_MULTIPLE_CHOICES: number;", "import_statement": "import { HTTP_STATUS_MOVED_PERMANENTLY } from 'http2';", "parent_symbol": null, "is_nested": false, "score": 0.71104324}]}