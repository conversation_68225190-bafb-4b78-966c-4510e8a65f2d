[{"symbol_name": "BaseProfile", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Base interface of profile. @typedef BaseProfile @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.BaseProfile", "import_statement": "import { bluetooth.BaseProfile } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.BaseProfile", "score": 0.7987922}, {"symbol_name": "BondStateParam", "symbol_type": "interface", "module_name": "@ohos.bluetoothManager", "is_default": false, "is_ets": false, "description": "Describes the class of a bluetooth device. @typedef BondStateParam @syscap SystemCapability.Communication.Bluetooth.Core @since 9 @deprecated since 10 @useinstead ohos.bluetooth.connection/connection.BondStateParam", "import_statement": "import { bluetoothManager.BondStateParam } from '@ohos.bluetoothManager';", "parent_symbol": "bluetoothManager", "is_nested": true, "full_name": "bluetoothManager.BondStateParam", "score": 0.6772797}, {"symbol_name": "DeviceClass", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the class of a bluetooth device. @typedef DeviceClass @syscap SystemCapability.Communication.Bluetooth.Core @since 8 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.DeviceClass", "import_statement": "import { bluetooth.DeviceClass } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.DeviceClass", "score": 0.67409015}]