{"first_query_time": 0.1001749038696289, "second_query_time": 0.0, "speedup": 1001.7490386962891, "results": [{"symbol_name": "<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@system.prompt", "is_default": false, "is_ets": false, "description": "Defines the prompt info of button. @interface But<PERSON> @syscap SystemCapability.ArkUI.ArkUI.Full @atomicservice @since 11", "import_statement": "import { Button } from '@system.prompt';", "parent_symbol": null, "is_nested": false, "score": 0.69622564}, {"symbol_name": "ButtonElement", "symbol_type": "interface", "module_name": "js.viewmodel", "is_default": false, "is_ets": false, "description": "The <button> component includes capsule, circle, text, arc, and download buttons. @interface ButtonElement @syscap SystemCapability.ArkUI.ArkUI.Full @since 4", "import_statement": "import { ButtonElement } from 'js.viewmodel';", "parent_symbol": null, "is_nested": false, "score": 0.6844307}, {"symbol_name": "ButtonType", "symbol_type": "enum", "module_name": "button", "is_default": false, "is_ets": false, "description": "Provides a button component. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 11", "import_statement": "import { ButtonType } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.6681042}]}