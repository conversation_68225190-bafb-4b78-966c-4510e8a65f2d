["test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_agent_chat_method", "test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_agent_initialization_no_agno", "test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_agent_initialization_success", "test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_all_tools_available", "test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_concurrent_access", "test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_singleton_reset", "test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_tool_execution_error", "test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_tool_execution_success", "test_codes/test_agent_comprehensive.py::TestArkTSAgent::test_tool_no_results", "test_codes/test_agent_comprehensive.py::TestIntegration::test_end_to_end_flow", "test_codes/test_comprehensive.py::TestComprehensive::test_01_basic_queries", "test_codes/test_comprehensive.py::TestComprehensive::test_02_component_searches", "test_codes/test_comprehensive.py::TestComprehensive::test_03_import_path_searches", "test_codes/test_comprehensive.py::TestComprehensive::test_04_symbol_type_searches", "test_codes/test_comprehensive.py::TestComprehensive::test_05_nested_symbol_searches", "test_codes/test_comprehensive.py::TestComprehensive::test_06_hybrid_searches", "test_codes/test_comprehensive.py::TestComprehensive::test_07_caching", "test_codes/test_comprehensive.py::TestComprehensive::test_08_enhanced_ranking", "test_codes/test_comprehensive.py::TestComprehensive::test_09_async_queries", "test_codes/test_comprehensive.py::TestComprehensive::test_10_formatting", "test_codes/test_comprehensive.py::TestComprehensive::test_11_agno_tools"]