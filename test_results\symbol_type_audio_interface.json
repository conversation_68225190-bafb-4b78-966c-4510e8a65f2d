[{"symbol_name": "AudioPlayer", "symbol_type": "interface", "module_name": "@ohos.multimedia.media", "is_default": false, "is_ets": false, "description": "Manages and plays audio. Before calling an AudioPlayer method, you must use createAudioPlayer() to create an AudioPlayer instance. @typedef AudioPlayer @syscap SystemCapability.Multimedia.Media.AudioPlayer @since 6 @deprecated since 9 @useinstead ohos.multimedia.media/media.AVPlayer", "import_statement": "import { media.AudioPlayer } from '@ohos.multimedia.media';", "parent_symbol": "media", "is_nested": true, "full_name": "media.AudioPlayer", "score": 0.62260807}, {"symbol_name": "AudioRecorderConfig", "symbol_type": "interface", "module_name": "@ohos.multimedia.media", "is_default": false, "is_ets": false, "description": "Provides the audio recorder configuration definitions. @typedef AudioRecorderConfig @syscap SystemCapability.Multimedia.Media.AudioRecorder @since 6 @deprecated since 9 @useinstead ohos.multimedia.media/media.AVRecorderConfig", "import_statement": "import { media.AudioRecorderConfig } from '@ohos.multimedia.media';", "parent_symbol": "media", "is_nested": true, "full_name": "media.AudioRecorderConfig", "score": 0.61954594}, {"symbol_name": "AudioCapturerOptions", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describes audio capturer configuration options. @typedef AudioCapturerOptions @syscap SystemCapability.Multimedia.Audio.Capturer @crossplatform @since 12", "import_statement": "import { audio.AudioCapturerOptions } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioCapturerOptions", "score": 0.6182161}]