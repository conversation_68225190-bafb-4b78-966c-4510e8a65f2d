[{"symbol_name": "ButtonType", "symbol_type": "enum", "module_name": "button", "is_default": false, "is_ets": false, "description": "Provides a button component. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 11", "import_statement": "import { ButtonType } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.6681042}, {"symbol_name": "ButtonRole", "symbol_type": "enum", "module_name": "button", "is_default": false, "is_ets": false, "description": "Enum for button role. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 12", "import_statement": "import { ButtonRole } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.63597226}, {"symbol_name": "PasteButtonOnClickResult", "symbol_type": "enum", "module_name": "paste_button", "is_default": false, "is_ets": false, "description": "Enumerates the click event results of the paste button. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @atomicservice @since 11", "import_statement": "import { PasteButtonOnClickResult } from 'paste_button';", "parent_symbol": null, "is_nested": false, "score": 0.63594204}]