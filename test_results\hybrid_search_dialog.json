{"standard": [{"symbol_name": "ShowDialogOptions", "symbol_type": "interface", "module_name": "@ohos.promptAction", "is_default": false, "is_ets": false, "description": "@typedef ShowDialogOptions @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { promptAction.ShowDialogOptions } from '@ohos.promptAction';", "parent_symbol": "promptAction", "is_nested": true, "full_name": "promptAction.ShowDialogOptions", "score": 0.6762278}, {"symbol_name": "WebElement", "symbol_type": "interface", "module_name": "js.viewmodel", "is_default": false, "is_ets": false, "description": "The <web> component is a container for displaying web page content. @interface WebElement @syscap SystemCapability.ArkUI.ArkUI.Full @since 6", "import_statement": "import { WebElement } from 'js.viewmodel';", "parent_symbol": null, "is_nested": false, "score": 0.67005414}, {"symbol_name": "OnContentWillChangeCallback", "symbol_type": "callback_type", "module_name": "@ohos.atomicservice.AtomicServiceTabs", "is_default": false, "is_ets": true, "description": "Callback type definition", "import_statement": "import type { OnContentWillChangeCallback } from '@ohos.atomicservice.AtomicServiceTabs';", "parent_symbol": null, "is_nested": false, "score": 0.6676252}], "hybrid": [{"symbol_name": "DialogAlignment", "symbol_type": "enum", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "The alignment of dialog, @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { DialogAlignment } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.6474429}, {"symbol_name": "AlertDialogParamWithOptions", "symbol_type": "interface", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "Defines the dialog param with options. @interface AlertDialogParamWithOptions @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { AlertDialogParamWithOptions } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.6336434}, {"symbol_name": "DismissDialogAction", "symbol_type": "interface", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "Component dialog dismiss action. @interface DismissDialogAction @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 12", "import_statement": "import { DismissDialogAction } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.6155079}]}