[{"symbol_name": "AlertDialog", "symbol_type": "class", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "Defines AlertDialog which uses show method to show alert dialog. @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { AlertDialog } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.6143059}, {"symbol_name": "TextPickerDialog", "symbol_type": "class", "module_name": "text_picker", "is_default": false, "is_ets": false, "description": "Defines TextPickerDialog which uses show method to show TextPicker dialog. @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { TextPickerDialog } from 'text_picker';", "parent_symbol": null, "is_nested": false, "score": 0.4841774}, {"symbol_name": "InterstitialDialogAction", "symbol_type": "class", "module_name": "@ohos.atomicservice.InterstitialDialogAction", "is_default": false, "is_ets": true, "description": "Declare dialog action. @syscap SystemCapability.ArkUI.ArkUI.Full @atomicservice @since 12 export", "import_statement": "import { InterstitialDialogAction } from '@ohos.atomicservice.InterstitialDialogAction';", "parent_symbol": null, "is_nested": false, "score": 0.47747844}]