[{"symbol_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the criteria for filtering scanning results can be set. @typedef ScanFilter @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.ScanFilter", "import_statement": "import { bluetooth.ScanFilter } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.ScanFilter", "score": 0.0}, {"symbol_name": "StateChangeParam", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Profile state change parameters. @typedef StateChangeParam @syscap SystemCapability.Communication.Bluetooth.Core @since 8 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.StateChangeParam", "import_statement": "import { bluetooth.StateChangeParam } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.StateChangeParam", "score": 0.0}, {"symbol_name": "DeviceClass", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the class of a bluetooth device. @typedef DeviceClass @syscap SystemCapability.Communication.Bluetooth.Core @since 8 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.DeviceClass", "import_statement": "import { bluetooth.DeviceClass } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.DeviceClass", "score": 0.0}]