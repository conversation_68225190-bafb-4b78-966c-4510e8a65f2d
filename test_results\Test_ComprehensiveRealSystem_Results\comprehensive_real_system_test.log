2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:0.6b
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../Information/default/openharmony/ets/api
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../Information/default/openharmony/ets/component
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../Information/default/openharmony/ets/build-tools
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../Information/default/hms/ets/api
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../comprehensive_dataset
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - INFO - Found 0 large ArkTS files for testing
2025-05-26 13:26:47,488 - ComprehensiveRealSystemTest - ERROR - No large ArkTS files found for testing
2025-05-26 13:27:00,944 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:27:00,944 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-26 13:27:00,944 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-26 13:27:00,944 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:0.6b
2025-05-26 13:27:00,945 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:27:00,945 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-26 13:27:00,945 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-26 13:27:00,945 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-26 13:27:00,946 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../Information/default/openharmony/ets/api
2025-05-26 13:27:00,946 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../Information/default/openharmony/ets/component
2025-05-26 13:27:00,946 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../Information/default/openharmony/ets/build-tools
2025-05-26 13:27:00,946 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../Information/default/hms/ets/api
2025-05-26 13:27:00,946 - ComprehensiveRealSystemTest - WARNING - Directory not found: ../comprehensive_dataset
2025-05-26 13:27:00,946 - ComprehensiveRealSystemTest - INFO - Found 0 large ArkTS files for testing
2025-05-26 13:27:00,946 - ComprehensiveRealSystemTest - ERROR - No large ArkTS files found for testing
2025-05-26 13:27:27,218 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:27:27,219 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-26 13:27:27,219 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-26 13:27:27,219 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:0.6b
2025-05-26 13:27:27,219 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:27:27,220 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-26 13:27:27,220 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-26 13:27:27,220 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-26 13:27:27,220 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/api
2025-05-26 13:27:27,223 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.appAccount.d.ts (136,704 bytes)
2025-05-26 13:27:27,223 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.osAccount.d.ts (59,446 bytes)
2025-05-26 13:27:27,226 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.arkui.UIContext.d.ts (123,406 bytes)
2025-05-26 13:27:27,227 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:27:27,227 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:27:27,227 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:27:27,227 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.buffer.d.ts (190,085 bytes)
2025-05-26 13:27:27,227 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:27:27,227 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEvent.d.ts (65,130 bytes)
2025-05-26 13:27:27,229 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEventManager.d.ts (68,365 bytes)
2025-05-26 13:27:27,229 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.contact.d.ts (143,754 bytes)
2025-05-26 13:27:27,229 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:27:27,229 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:27:27,229 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:27:27,229 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:27:27,229 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.display.d.ts (50,958 bytes)
2025-05-26 13:27:27,232 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:27:27,232 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.geoLocationManager.d.ts (95,526 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts (149,907 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts (50,405 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.i18n.d.ts (111,611 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethod.d.ts (75,816 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethodEngine.d.ts (82,240 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.intl.d.ts (116,417 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts (51,152 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.nfc.tag.d.ts (60,146 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.notificationManager.d.ts (67,491 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.pasteboard.d.ts (57,975 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:27:27,233 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.resourceManager.d.ts (245,843 bytes)
2025-05-26 13:27:27,239 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.router.d.ts (50,688 bytes)
2025-05-26 13:27:27,239 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.rpc.d.ts (170,228 bytes)
2025-05-26 13:27:27,239 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cert.d.ts (214,222 bytes)
2025-05-26 13:27:27,240 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts (336,615 bytes)
2025-05-26 13:27:27,240 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.huks.d.ts (186,266 bytes)
2025-05-26 13:27:27,240 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:27:27,242 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.UiTest.d.ts (158,743 bytes)
2025-05-26 13:27:27,242 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.userIAM.userAuth.d.ts (55,963 bytes)
2025-05-26 13:27:27,242 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.util.d.ts (198,770 bytes)
2025-05-26 13:27:27,243 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.netErrorList.d.ts (61,804 bytes)
2025-05-26 13:27:27,243 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts (267,704 bytes)
2025-05-26 13:27:27,243 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:27:27,243 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.window.d.ts (286,762 bytes)
2025-05-26 13:27:27,243 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.worker.d.ts (86,613 bytes)
2025-05-26 13:27:27,243 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.zlib.d.ts (98,263 bytes)
2025-05-26 13:27:27,246 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\AbilityDelegator.d.ts (66,895 bytes)
2025-05-26 13:27:27,246 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\UIAbilityContext.d.ts (109,096 bytes)
2025-05-26 13:27:27,251 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\tag\nfctech.d.ts (69,043 bytes)
2025-05-26 13:27:27,252 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/component
2025-05-26 13:27:27,252 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\canvas.d.ts (209,139 bytes)
2025-05-26 13:27:27,253 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common.d.ts (566,343 bytes)
2025-05-26 13:27:27,253 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:27:27,253 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\enums.d.ts (205,914 bytes)
2025-05-26 13:27:27,253 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\gesture.d.ts (97,133 bytes)
2025-05-26 13:27:27,254 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\navigation.d.ts (83,995 bytes)
2025-05-26 13:27:27,254 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:27:27,255 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\swiper.d.ts (55,612 bytes)
2025-05-26 13:27:27,255 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\text_input.d.ts (62,924 bytes)
2025-05-26 13:27:27,255 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\units.d.ts (68,870 bytes)
2025-05-26 13:27:27,255 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\web.d.ts (231,955 bytes)
2025-05-26 13:27:27,255 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/build-tools
2025-05-26 13:27:27,255 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\aot\src\lib_ark_builtins.d.ts (54,703 bytes)
2025-05-26 13:27:27,255 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts (842,681 bytes)
2025-05-26 13:27:27,273 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.es5.d.ts (212,312 bytes)
2025-05-26 13:27:27,277 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.webworker.d.ts (259,617 bytes)
2025-05-26 13:27:27,277 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\protocol.d.ts (98,216 bytes)
2025-05-26 13:27:27,277 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts (587,234 bytes)
2025-05-26 13:27:27,277 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts (447,512 bytes)
2025-05-26 13:27:27,279 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts (447,499 bytes)
2025-05-26 13:27:27,304 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\canvas.d.ts (209,151 bytes)
2025-05-26 13:27:27,306 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts (566,649 bytes)
2025-05-26 13:27:27,306 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:27:27,307 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\enums.d.ts (205,914 bytes)
2025-05-26 13:27:27,307 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\gesture.d.ts (97,133 bytes)
2025-05-26 13:27:27,309 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\navigation.d.ts (84,079 bytes)
2025-05-26 13:27:27,309 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:27:27,309 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\swiper.d.ts (55,612 bytes)
2025-05-26 13:27:27,311 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\text_input.d.ts (62,924 bytes)
2025-05-26 13:27:27,311 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\units.d.ts (68,936 bytes)
2025-05-26 13:27:27,311 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\web.d.ts (231,967 bytes)
2025-05-26 13:27:27,413 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index-legacy.d.ts (168,429 bytes)
2025-05-26 13:27:27,413 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts (611,184 bytes)
2025-05-26 13:27:27,493 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts (52,155 bytes)
2025-05-26 13:27:27,493 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts (52,150 bytes)
2025-05-26 13:27:27,493 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\buffer.d.ts (107,224 bytes)
2025-05-26 13:27:27,493 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\child_process.d.ts (67,998 bytes)
2025-05-26 13:27:27,502 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\crypto.d.ts (183,978 bytes)
2025-05-26 13:27:27,503 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs.d.ts (182,973 bytes)
2025-05-26 13:27:27,504 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http.d.ts (79,056 bytes)
2025-05-26 13:27:27,504 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http2.d.ts (113,815 bytes)
2025-05-26 13:27:27,505 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\inspector.d.ts (125,369 bytes)
2025-05-26 13:27:27,506 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\process.d.ts (73,684 bytes)
2025-05-26 13:27:27,508 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\stream.d.ts (70,767 bytes)
2025-05-26 13:27:27,509 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\tls.d.ts (54,923 bytes)
2025-05-26 13:27:27,510 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\util.d.ts (82,275 bytes)
2025-05-26 13:27:27,511 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts (53,450 bytes)
2025-05-26 13:27:27,517 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\buffer.d.ts (107,224 bytes)
2025-05-26 13:27:27,517 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\child_process.d.ts (67,998 bytes)
2025-05-26 13:27:27,518 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\crypto.d.ts (183,913 bytes)
2025-05-26 13:27:27,519 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs.d.ts (182,973 bytes)
2025-05-26 13:27:27,520 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http.d.ts (79,056 bytes)
2025-05-26 13:27:27,520 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http2.d.ts (113,815 bytes)
2025-05-26 13:27:27,520 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\inspector.d.ts (125,369 bytes)
2025-05-26 13:27:27,520 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\process.d.ts (73,684 bytes)
2025-05-26 13:27:27,523 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\stream.d.ts (72,452 bytes)
2025-05-26 13:27:27,523 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\tls.d.ts (54,923 bytes)
2025-05-26 13:27:27,525 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\util.d.ts (82,262 bytes)
2025-05-26 13:27:27,528 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts (53,380 bytes)
2025-05-26 13:27:27,578 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\dist\generated\ast-spec.d.ts (68,378 bytes)
2025-05-26 13:27:27,578 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\_ts3.4\dist\generated\ast-spec.d.ts (70,137 bytes)
2025-05-26 13:27:28,127 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts (816,786 bytes)
2025-05-26 13:27:28,144 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.es5.d.ts (213,196 bytes)
2025-05-26 13:27:28,146 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts (272,256 bytes)
2025-05-26 13:27:28,147 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\protocol.d.ts (107,934 bytes)
2025-05-26 13:27:28,147 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts (747,710 bytes)
2025-05-26 13:27:28,147 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts (598,789 bytes)
2025-05-26 13:27:28,148 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts (598,776 bytes)
2025-05-26 13:27:28,161 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts (335,559 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/api
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:27:28,210 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/kits
2025-05-26 13:27:28,221 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/hms/ets/component
2025-05-26 13:27:28,221 - ComprehensiveRealSystemTest - INFO - Searching in: comprehensive_dataset
2025-05-26 13:27:28,222 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\basic_ui\text_input.d.ts (62,924 bytes)
2025-05-26 13:27:28,223 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\container\swiper.d.ts (55,612 bytes)
2025-05-26 13:27:28,223 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:27:28,224 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:27:28,224 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:27:28,224 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:27:28,224 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:27:28,226 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\form\text_input.d.ts (62,924 bytes)
2025-05-26 13:27:28,229 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:27:28,229 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:27:28,230 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:27:28,230 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:27:28,230 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:27:28,231 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:27:28,231 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:27:28,231 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:27:28,231 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:27:28,232 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:27:28,233 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:27:28,233 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:27:28,233 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:27:28,234 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:27:28,234 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:27:28,234 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:27:28,234 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:27:28,234 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:27:28,234 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:27:28,236 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\navigation\navigation.d.ts (83,995 bytes)
2025-05-26 13:27:28,236 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:27:28,236 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:27:28,237 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:27:28,237 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:27:28,237 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:27:28,238 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:27:28,238 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:27:28,238 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:27:28,239 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:27:28,239 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:27:28,239 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:27:28,242 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\system\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:27:28,243 - ComprehensiveRealSystemTest - INFO - Found 30 large ArkTS files for testing
2025-05-26 13:27:28,243 - ComprehensiveRealSystemTest - INFO - Found 30 large files for testing
2025-05-26 13:27:28,243 - ComprehensiveRealSystemTest - INFO - Step 2: Testing parsing capabilities...
2025-05-26 13:27:28,244 - ComprehensiveRealSystemTest - INFO - === Testing Parsing Capabilities ===
2025-05-26 13:27:28,244 - ComprehensiveRealSystemTest - INFO - Parsing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:27:28,309 - ComprehensiveRealSystemTest - INFO - Parsed 196 symbols in 0.065s
2025-05-26 13:27:28,309 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'type': 1, 'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 1, 'union_type': 176, 'intersection_type': 1}
2025-05-26 13:27:28,309 - ComprehensiveRealSystemTest - INFO - Parsing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:27:28,377 - ComprehensiveRealSystemTest - INFO - Parsed 210 symbols in 0.068s
2025-05-26 13:27:28,377 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 2, 'union_type': 190, 'intersection_type': 1}
2025-05-26 13:27:28,377 - ComprehensiveRealSystemTest - INFO - Parsing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:27:28,562 - ComprehensiveRealSystemTest - INFO - Parsed 2293 symbols in 0.186s
2025-05-26 13:27:28,562 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 45, 'interface': 1244, 'type': 185, 'function': 50, 'namespace': 110, 'enum': 41, 'const': 445, 'reexport_all': 1, 'callback_type': 12, 'union_type': 145, 'intersection_type': 12, 'export_assignment': 3}
2025-05-26 13:27:28,562 - ComprehensiveRealSystemTest - INFO - Parsing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:27:28,616 - ComprehensiveRealSystemTest - INFO - Parsed 147 symbols in 0.054s
2025-05-26 13:27:28,616 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'const': 88, 'union_type': 57}
2025-05-26 13:27:28,616 - ComprehensiveRealSystemTest - INFO - Parsing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:27:28,766 - ComprehensiveRealSystemTest - INFO - Parsed 2009 symbols in 0.149s
2025-05-26 13:27:28,766 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 3}
2025-05-26 13:27:28,766 - ComprehensiveRealSystemTest - INFO - Parsing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:27:28,915 - ComprehensiveRealSystemTest - INFO - Parsed 2008 symbols in 0.150s
2025-05-26 13:27:28,915 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 2}
2025-05-26 13:27:28,915 - ComprehensiveRealSystemTest - INFO - Parsing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:27:29,044 - ComprehensiveRealSystemTest - INFO - Parsed 1940 symbols in 0.129s
2025-05-26 13:27:29,044 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 27, 'interface': 1106, 'type': 169, 'function': 36, 'namespace': 41, 'enum': 39, 'const': 363, 'reexport_all': 1, 'callback_type': 12, 'union_type': 133, 'intersection_type': 11, 'export_assignment': 2}
2025-05-26 13:27:29,044 - ComprehensiveRealSystemTest - INFO - Parsing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 13:27:29,128 - ComprehensiveRealSystemTest - INFO - Parsed 243 symbols in 0.084s
2025-05-26 13:27:29,128 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2}
2025-05-26 13:27:29,128 - ComprehensiveRealSystemTest - INFO - Parsing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 13:27:29,210 - ComprehensiveRealSystemTest - INFO - Parsed 244 symbols in 0.082s
2025-05-26 13:27:29,210 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2, 'component': 1}
2025-05-26 13:27:29,210 - ComprehensiveRealSystemTest - INFO - Parsing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:27:29,315 - ComprehensiveRealSystemTest - INFO - Parsed 1663 symbols in 0.105s
2025-05-26 13:27:29,315 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 2}
2025-05-26 13:27:29,315 - ComprehensiveRealSystemTest - INFO - Parsing file 11/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:27:29,416 - ComprehensiveRealSystemTest - INFO - Parsed 1662 symbols in 0.101s
2025-05-26 13:27:29,416 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 1}
2025-05-26 13:27:29,416 - ComprehensiveRealSystemTest - INFO - Parsing file 12/30: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-26 13:27:29,451 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.034s
2025-05-26 13:27:29,451 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:27:29,451 - ComprehensiveRealSystemTest - INFO - Parsing file 13/30: comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-26 13:27:29,485 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.034s
2025-05-26 13:27:29,485 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:27:29,485 - ComprehensiveRealSystemTest - INFO - Parsing file 14/30: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-26 13:27:29,530 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.044s
2025-05-26 13:27:29,530 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:27:29,530 - ComprehensiveRealSystemTest - INFO - Parsing file 15/30: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-26 13:27:29,597 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.067s
2025-05-26 13:27:29,597 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:27:29,597 - ComprehensiveRealSystemTest - INFO - Parsing file 16/30: Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-26 13:27:29,681 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.085s
2025-05-26 13:27:29,681 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:27:29,681 - ComprehensiveRealSystemTest - INFO - Parsing file 17/30: comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-26 13:27:29,767 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.085s
2025-05-26 13:27:29,767 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:27:29,767 - ComprehensiveRealSystemTest - INFO - Parsing file 18/30: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-26 13:27:29,814 - ComprehensiveRealSystemTest - INFO - Parsed 67 symbols in 0.047s
2025-05-26 13:27:29,814 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 6, 'interface': 59, 'export_assignment': 1}
2025-05-26 13:27:29,814 - ComprehensiveRealSystemTest - INFO - Parsing file 19/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-26 13:27:29,879 - ComprehensiveRealSystemTest - INFO - Parsed 570 symbols in 0.065s
2025-05-26 13:27:29,879 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 122, 'interface': 263, 'type': 1, 'namespace': 65, 'const': 26, 'callback_type': 1, 'union_type': 55, 'intersection_type': 36, 'export_assignment': 1}
2025-05-26 13:27:29,879 - ComprehensiveRealSystemTest - INFO - Parsing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-26 13:27:29,919 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.040s
2025-05-26 13:27:29,929 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:27:29,929 - ComprehensiveRealSystemTest - INFO - Parsing file 21/30: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-26 13:27:29,969 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.040s
2025-05-26 13:27:29,969 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:27:29,969 - ComprehensiveRealSystemTest - INFO - Parsing file 22/30: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-26 13:27:30,026 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.057s
2025-05-26 13:27:30,026 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:27:30,026 - ComprehensiveRealSystemTest - INFO - Parsing file 23/30: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-26 13:27:30,070 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.044s
2025-05-26 13:27:30,070 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:27:30,070 - ComprehensiveRealSystemTest - INFO - Parsing file 24/30: Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-26 13:27:30,117 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.046s
2025-05-26 13:27:30,117 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 44, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:27:30,117 - ComprehensiveRealSystemTest - INFO - Parsing file 25/30: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-26 13:27:30,153 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.036s
2025-05-26 13:27:30,153 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:27:30,153 - ComprehensiveRealSystemTest - INFO - Parsing file 26/30: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-26 13:27:30,198 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.045s
2025-05-26 13:27:30,198 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:27:30,198 - ComprehensiveRealSystemTest - INFO - Parsing file 27/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-26 13:27:30,214 - ComprehensiveRealSystemTest - INFO - Parsed 105 symbols in 0.016s
2025-05-26 13:27:30,214 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 14, 'union_type': 90}
2025-05-26 13:27:30,214 - ComprehensiveRealSystemTest - INFO - Parsing file 28/30: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-26 13:27:30,263 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.048s
2025-05-26 13:27:30,264 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:27:30,264 - ComprehensiveRealSystemTest - INFO - Parsing file 29/30: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-26 13:27:30,303 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.039s
2025-05-26 13:27:30,305 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:27:30,305 - ComprehensiveRealSystemTest - INFO - Parsing file 30/30: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-26 13:27:30,347 - ComprehensiveRealSystemTest - INFO - Parsed 60 symbols in 0.042s
2025-05-26 13:27:30,348 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 25, 'interface': 31, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:27:30,348 - ComprehensiveRealSystemTest - INFO - Parsing completed: 30/30 files successful
2025-05-26 13:27:30,348 - ComprehensiveRealSystemTest - INFO - Total symbols parsed: 14688
2025-05-26 13:27:30,348 - ComprehensiveRealSystemTest - INFO - Symbol types found: {'type': 1132, 'namespace': 604, 'interface': 7257, 'const': 3102, 'callback_type': 109, 'union_type': 1408, 'intersection_type': 87, 'class': 405, 'function': 234, 'enum': 312, 'reexport_all': 6, 'export_assignment': 31, 'component': 1}
2025-05-26 13:27:30,348 - ComprehensiveRealSystemTest - INFO - Step 3: Testing indexing capabilities...
2025-05-26 13:27:30,348 - ComprehensiveRealSystemTest - INFO - === Testing Indexing Capabilities ===
2025-05-26 13:27:30,831 - ComprehensiveRealSystemTest - INFO - Qdrant collection created successfully
2025-05-26 13:27:30,832 - ComprehensiveRealSystemTest - INFO - Indexing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:27:36,058 - ComprehensiveRealSystemTest - INFO - Indexed 196 symbols in 5.226s
2025-05-26 13:27:36,058 - ComprehensiveRealSystemTest - INFO - Indexing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:27:42,340 - ComprehensiveRealSystemTest - INFO - Indexed 210 symbols in 6.266s
2025-05-26 13:27:42,340 - ComprehensiveRealSystemTest - INFO - Indexing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:29:00,300 - ComprehensiveRealSystemTest - INFO - Indexed 2293 symbols in 77.961s
2025-05-26 13:29:00,300 - ComprehensiveRealSystemTest - INFO - Indexing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:29:06,681 - ComprehensiveRealSystemTest - INFO - Indexed 147 symbols in 6.381s
2025-05-26 13:29:06,681 - ComprehensiveRealSystemTest - INFO - Indexing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:30:23,474 - ComprehensiveRealSystemTest - INFO - Indexed 2009 symbols in 76.793s
2025-05-26 13:30:23,474 - ComprehensiveRealSystemTest - INFO - Indexing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:31:41,662 - ComprehensiveRealSystemTest - INFO - Indexed 2008 symbols in 78.188s
2025-05-26 13:31:41,662 - ComprehensiveRealSystemTest - INFO - Indexing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:32:48,669 - ComprehensiveRealSystemTest - INFO - Indexed 1940 symbols in 67.007s
2025-05-26 13:32:48,669 - ComprehensiveRealSystemTest - INFO - Indexing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 13:32:55,438 - ComprehensiveRealSystemTest - INFO - Indexed 243 symbols in 6.770s
2025-05-26 13:32:55,444 - ComprehensiveRealSystemTest - INFO - Indexing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 13:33:02,434 - ComprehensiveRealSystemTest - INFO - Indexed 244 symbols in 6.990s
2025-05-26 13:33:02,434 - ComprehensiveRealSystemTest - INFO - Indexing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:33:57,895 - ComprehensiveRealSystemTest - INFO - Indexed 1663 symbols in 55.461s
2025-05-26 13:33:57,897 - ComprehensiveRealSystemTest - INFO - Indexing file 11/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:34:53,299 - ComprehensiveRealSystemTest - INFO - Indexed 1662 symbols in 55.401s
2025-05-26 13:34:53,299 - ComprehensiveRealSystemTest - INFO - Indexing file 12/30: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-26 13:34:54,547 - ComprehensiveRealSystemTest - INFO - Indexed 40 symbols in 1.248s
2025-05-26 13:34:54,547 - ComprehensiveRealSystemTest - INFO - Indexing file 13/30: comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-26 13:34:55,465 - ComprehensiveRealSystemTest - INFO - Indexed 40 symbols in 0.918s
2025-05-26 13:34:55,465 - ComprehensiveRealSystemTest - INFO - Indexing file 14/30: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-26 13:34:56,567 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.102s
2025-05-26 13:34:56,567 - ComprehensiveRealSystemTest - INFO - Indexing file 15/30: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-26 13:34:58,011 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.444s
2025-05-26 13:34:58,011 - ComprehensiveRealSystemTest - INFO - Indexing file 16/30: Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-26 13:35:09,226 - ComprehensiveRealSystemTest - INFO - Indexed 385 symbols in 11.215s
2025-05-26 13:35:09,226 - ComprehensiveRealSystemTest - INFO - Indexing file 17/30: comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-26 13:35:19,823 - ComprehensiveRealSystemTest - INFO - Indexed 385 symbols in 10.597s
2025-05-26 13:35:19,824 - ComprehensiveRealSystemTest - INFO - Indexing file 18/30: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-26 13:35:21,404 - ComprehensiveRealSystemTest - INFO - Indexed 67 symbols in 1.579s
2025-05-26 13:35:21,404 - ComprehensiveRealSystemTest - INFO - Indexing file 19/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-26 13:35:39,906 - ComprehensiveRealSystemTest - INFO - Indexed 570 symbols in 18.502s
2025-05-26 13:35:39,906 - ComprehensiveRealSystemTest - INFO - Indexing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-26 13:35:40,924 - ComprehensiveRealSystemTest - INFO - Indexed 33 symbols in 1.017s
2025-05-26 13:35:40,924 - ComprehensiveRealSystemTest - INFO - Indexing file 21/30: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-26 13:35:41,945 - ComprehensiveRealSystemTest - INFO - Indexed 33 symbols in 1.022s
2025-05-26 13:35:41,945 - ComprehensiveRealSystemTest - INFO - Indexing file 22/30: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-26 13:35:42,733 - ComprehensiveRealSystemTest - INFO - Indexed 24 symbols in 0.787s
2025-05-26 13:35:42,733 - ComprehensiveRealSystemTest - INFO - Indexing file 23/30: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-26 13:35:43,415 - ComprehensiveRealSystemTest - INFO - Indexed 24 symbols in 0.682s
2025-05-26 13:35:43,415 - ComprehensiveRealSystemTest - INFO - Indexing file 24/30: Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-26 13:35:44,581 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.166s
2025-05-26 13:35:44,581 - ComprehensiveRealSystemTest - INFO - Indexing file 25/30: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-26 13:35:45,726 - ComprehensiveRealSystemTest - INFO - Indexed 45 symbols in 1.145s
2025-05-26 13:35:45,726 - ComprehensiveRealSystemTest - INFO - Indexing file 26/30: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-26 13:35:46,886 - ComprehensiveRealSystemTest - INFO - Indexed 45 symbols in 1.160s
2025-05-26 13:35:46,886 - ComprehensiveRealSystemTest - INFO - Indexing file 27/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-26 13:35:49,976 - ComprehensiveRealSystemTest - INFO - Indexed 105 symbols in 3.090s
2025-05-26 13:35:49,976 - ComprehensiveRealSystemTest - INFO - Indexing file 28/30: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-26 13:35:51,161 - ComprehensiveRealSystemTest - INFO - Indexed 38 symbols in 1.184s
2025-05-26 13:35:51,161 - ComprehensiveRealSystemTest - INFO - Indexing file 29/30: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-26 13:35:52,246 - ComprehensiveRealSystemTest - INFO - Indexed 38 symbols in 1.086s
2025-05-26 13:35:52,246 - ComprehensiveRealSystemTest - INFO - Indexing file 30/30: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-26 13:35:53,798 - ComprehensiveRealSystemTest - INFO - Indexed 60 symbols in 1.552s
2025-05-26 13:35:53,798 - ComprehensiveRealSystemTest - INFO - Indexing completed: 30/30 files successful
2025-05-26 13:35:53,798 - ComprehensiveRealSystemTest - INFO - Total symbols indexed: 14688
2025-05-26 13:35:53,798 - ComprehensiveRealSystemTest - INFO - Step 4: Testing agent capabilities...
2025-05-26 13:35:53,798 - ComprehensiveRealSystemTest - INFO - === Testing Agent Capabilities ===
2025-05-26 13:35:53,817 - ComprehensiveRealSystemTest - INFO - Testing query: 'Button' (type: component)
2025-05-26 13:35:53,883 - ComprehensiveRealSystemTest - INFO - Query successful in 0.066s, results length: 17
2025-05-26 13:35:53,883 - ComprehensiveRealSystemTest - INFO - Testing query: 'Dialog' (type: component)
2025-05-26 13:35:53,917 - ComprehensiveRealSystemTest - INFO - Query successful in 0.034s, results length: 17
2025-05-26 13:35:53,917 - ComprehensiveRealSystemTest - INFO - Testing query: 'Text' (type: component)
2025-05-26 13:35:53,950 - ComprehensiveRealSystemTest - INFO - Query successful in 0.032s, results length: 17
2025-05-26 13:35:53,950 - ComprehensiveRealSystemTest - INFO - Testing query: 'Image' (type: component)
2025-05-26 13:35:53,983 - ComprehensiveRealSystemTest - INFO - Query successful in 0.033s, results length: 17
2025-05-26 13:35:53,983 - ComprehensiveRealSystemTest - INFO - Testing query: 'List' (type: component)
2025-05-26 13:35:54,018 - ComprehensiveRealSystemTest - INFO - Query successful in 0.036s, results length: 17
2025-05-26 13:35:54,018 - ComprehensiveRealSystemTest - INFO - Testing query: 'UIAbility' (type: api)
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - ERROR - Error testing query 'UIAbility': 'Function' object is not callable
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - INFO - Testing query: 'router' (type: api)
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - ERROR - Error testing query 'router': 'Function' object is not callable
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - INFO - Testing query: 'preferences' (type: api)
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - ERROR - Error testing query 'preferences': 'Function' object is not callable
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - INFO - Testing query: 'http' (type: api)
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - ERROR - Error testing query 'http': 'Function' object is not callable
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - INFO - Testing query: 'bluetooth' (type: api)
2025-05-26 13:35:54,019 - ComprehensiveRealSystemTest - ERROR - Error testing query 'bluetooth': 'Function' object is not callable
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.app.ability.UIAbility' (type: import_path)
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.app.ability.UIAbility': 'Function' object is not callable
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.router' (type: import_path)
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.router': 'Function' object is not callable
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.data.preferences' (type: import_path)
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.data.preferences': 'Function' object is not callable
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.net.http' (type: import_path)
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.net.http': 'Function' object is not callable
2025-05-26 13:35:54,020 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.bluetooth' (type: import_path)
2025-05-26 13:35:54,021 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.bluetooth': 'Function' object is not callable
2025-05-26 13:35:54,021 - ComprehensiveRealSystemTest - INFO - Testing query: 'file system' (type: api)
2025-05-26 13:35:54,021 - ComprehensiveRealSystemTest - ERROR - Error testing query 'file system': 'Function' object is not callable
2025-05-26 13:35:54,021 - ComprehensiveRealSystemTest - INFO - Testing query: 'data storage' (type: api)
2025-05-26 13:35:54,021 - ComprehensiveRealSystemTest - ERROR - Error testing query 'data storage': 'Function' object is not callable
2025-05-26 13:35:54,021 - ComprehensiveRealSystemTest - INFO - Testing query: 'network request' (type: api)
2025-05-26 13:35:54,021 - ComprehensiveRealSystemTest - ERROR - Error testing query 'network request': 'Function' object is not callable
2025-05-26 13:35:54,022 - ComprehensiveRealSystemTest - INFO - Testing query: 'device sensor' (type: api)
2025-05-26 13:35:54,022 - ComprehensiveRealSystemTest - ERROR - Error testing query 'device sensor': 'Function' object is not callable
2025-05-26 13:35:54,022 - ComprehensiveRealSystemTest - INFO - Testing query: 'multimedia audio' (type: api)
2025-05-26 13:35:54,022 - ComprehensiveRealSystemTest - ERROR - Error testing query 'multimedia audio': 'Function' object is not callable
2025-05-26 13:35:54,024 - ComprehensiveRealSystemTest - INFO - Agent testing completed: 5/20 queries successful
2025-05-26 13:35:54,025 - ComprehensiveRealSystemTest - INFO - === Generating Comprehensive Report ===
2025-05-26 13:35:54,029 - ComprehensiveRealSystemTest - INFO - Comprehensive report saved to test_results/Test_ComprehensiveRealSystem_Results/
2025-05-26 13:35:54,029 - ComprehensiveRealSystemTest - INFO - Test completed successfully!
2025-05-26 13:35:54,029 - ComprehensiveRealSystemTest - INFO - Comprehensive test completed in 506.81 seconds
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:0.6b
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/api
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.appAccount.d.ts (136,704 bytes)
2025-05-26 13:47:29,578 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.osAccount.d.ts (59,446 bytes)
2025-05-26 13:47:29,586 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.arkui.UIContext.d.ts (123,406 bytes)
2025-05-26 13:47:29,586 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:47:29,587 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:47:29,587 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:47:29,587 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.buffer.d.ts (190,085 bytes)
2025-05-26 13:47:29,588 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:47:29,588 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEvent.d.ts (65,130 bytes)
2025-05-26 13:47:29,588 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEventManager.d.ts (68,365 bytes)
2025-05-26 13:47:29,588 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.contact.d.ts (143,754 bytes)
2025-05-26 13:47:29,589 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:47:29,589 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:47:29,589 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:47:29,589 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:47:29,590 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.display.d.ts (50,958 bytes)
2025-05-26 13:47:29,591 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:47:29,591 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:47:29,592 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:47:29,593 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.geoLocationManager.d.ts (95,526 bytes)
2025-05-26 13:47:29,594 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts (149,907 bytes)
2025-05-26 13:47:29,594 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts (50,405 bytes)
2025-05-26 13:47:29,594 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.i18n.d.ts (111,611 bytes)
2025-05-26 13:47:29,595 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethod.d.ts (75,816 bytes)
2025-05-26 13:47:29,595 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethodEngine.d.ts (82,240 bytes)
2025-05-26 13:47:29,595 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.intl.d.ts (116,417 bytes)
2025-05-26 13:47:29,596 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:47:29,596 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:47:29,596 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:47:29,596 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:47:29,596 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:47:29,597 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:47:29,597 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts (51,152 bytes)
2025-05-26 13:47:29,597 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:47:29,598 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:47:29,598 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:47:29,598 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.nfc.tag.d.ts (60,146 bytes)
2025-05-26 13:47:29,598 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.notificationManager.d.ts (67,491 bytes)
2025-05-26 13:47:29,599 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.pasteboard.d.ts (57,975 bytes)
2025-05-26 13:47:29,599 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:47:29,600 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.resourceManager.d.ts (245,843 bytes)
2025-05-26 13:47:29,600 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.router.d.ts (50,688 bytes)
2025-05-26 13:47:29,600 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.rpc.d.ts (170,228 bytes)
2025-05-26 13:47:29,601 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cert.d.ts (214,222 bytes)
2025-05-26 13:47:29,601 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts (336,615 bytes)
2025-05-26 13:47:29,601 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.huks.d.ts (186,266 bytes)
2025-05-26 13:47:29,601 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:47:29,602 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.UiTest.d.ts (158,743 bytes)
2025-05-26 13:47:29,602 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.userIAM.userAuth.d.ts (55,963 bytes)
2025-05-26 13:47:29,603 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.util.d.ts (198,770 bytes)
2025-05-26 13:47:29,604 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.netErrorList.d.ts (61,804 bytes)
2025-05-26 13:47:29,604 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts (267,704 bytes)
2025-05-26 13:47:29,604 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:47:29,604 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.window.d.ts (286,762 bytes)
2025-05-26 13:47:29,604 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.worker.d.ts (86,613 bytes)
2025-05-26 13:47:29,605 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.zlib.d.ts (98,263 bytes)
2025-05-26 13:47:29,610 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\AbilityDelegator.d.ts (66,895 bytes)
2025-05-26 13:47:29,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\UIAbilityContext.d.ts (109,096 bytes)
2025-05-26 13:47:29,631 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\tag\nfctech.d.ts (69,043 bytes)
2025-05-26 13:47:29,633 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/component
2025-05-26 13:47:29,634 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\canvas.d.ts (209,139 bytes)
2025-05-26 13:47:29,635 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common.d.ts (566,343 bytes)
2025-05-26 13:47:29,635 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:47:29,636 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\enums.d.ts (205,914 bytes)
2025-05-26 13:47:29,637 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\gesture.d.ts (97,133 bytes)
2025-05-26 13:47:29,639 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\navigation.d.ts (83,995 bytes)
2025-05-26 13:47:29,640 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:47:29,642 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\swiper.d.ts (55,612 bytes)
2025-05-26 13:47:29,643 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\text_input.d.ts (62,924 bytes)
2025-05-26 13:47:29,644 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\units.d.ts (68,870 bytes)
2025-05-26 13:47:29,644 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\web.d.ts (231,955 bytes)
2025-05-26 13:47:29,646 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/build-tools
2025-05-26 13:47:29,646 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\aot\src\lib_ark_builtins.d.ts (54,703 bytes)
2025-05-26 13:47:29,649 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts (842,681 bytes)
2025-05-26 13:47:29,651 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.es5.d.ts (212,312 bytes)
2025-05-26 13:47:29,654 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.webworker.d.ts (259,617 bytes)
2025-05-26 13:47:29,654 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\protocol.d.ts (98,216 bytes)
2025-05-26 13:47:29,654 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts (587,234 bytes)
2025-05-26 13:47:29,655 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts (447,512 bytes)
2025-05-26 13:47:29,655 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts (447,499 bytes)
2025-05-26 13:47:29,666 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\canvas.d.ts (209,151 bytes)
2025-05-26 13:47:29,667 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts (566,649 bytes)
2025-05-26 13:47:29,667 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:47:29,670 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\enums.d.ts (205,914 bytes)
2025-05-26 13:47:29,670 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\gesture.d.ts (97,133 bytes)
2025-05-26 13:47:29,672 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\navigation.d.ts (84,079 bytes)
2025-05-26 13:47:29,673 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:47:29,679 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\swiper.d.ts (55,612 bytes)
2025-05-26 13:47:29,680 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\text_input.d.ts (62,924 bytes)
2025-05-26 13:47:29,680 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\units.d.ts (68,936 bytes)
2025-05-26 13:47:29,681 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\web.d.ts (231,967 bytes)
2025-05-26 13:47:29,715 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index-legacy.d.ts (168,429 bytes)
2025-05-26 13:47:29,715 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts (611,184 bytes)
2025-05-26 13:47:29,737 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts (52,155 bytes)
2025-05-26 13:47:29,741 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts (52,150 bytes)
2025-05-26 13:47:29,743 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\buffer.d.ts (107,224 bytes)
2025-05-26 13:47:29,743 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\child_process.d.ts (67,998 bytes)
2025-05-26 13:47:29,743 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\crypto.d.ts (183,978 bytes)
2025-05-26 13:47:29,743 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs.d.ts (182,973 bytes)
2025-05-26 13:47:29,744 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http.d.ts (79,056 bytes)
2025-05-26 13:47:29,744 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http2.d.ts (113,815 bytes)
2025-05-26 13:47:29,744 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\inspector.d.ts (125,369 bytes)
2025-05-26 13:47:29,745 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\process.d.ts (73,684 bytes)
2025-05-26 13:47:29,746 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\stream.d.ts (70,767 bytes)
2025-05-26 13:47:29,746 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\tls.d.ts (54,923 bytes)
2025-05-26 13:47:29,747 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\util.d.ts (82,275 bytes)
2025-05-26 13:47:29,748 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts (53,450 bytes)
2025-05-26 13:47:29,750 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\buffer.d.ts (107,224 bytes)
2025-05-26 13:47:29,750 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\child_process.d.ts (67,998 bytes)
2025-05-26 13:47:29,751 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\crypto.d.ts (183,913 bytes)
2025-05-26 13:47:29,752 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs.d.ts (182,973 bytes)
2025-05-26 13:47:29,753 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http.d.ts (79,056 bytes)
2025-05-26 13:47:29,753 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http2.d.ts (113,815 bytes)
2025-05-26 13:47:29,753 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\inspector.d.ts (125,369 bytes)
2025-05-26 13:47:29,754 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\process.d.ts (73,684 bytes)
2025-05-26 13:47:29,754 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\stream.d.ts (72,452 bytes)
2025-05-26 13:47:29,755 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\tls.d.ts (54,923 bytes)
2025-05-26 13:47:29,755 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\util.d.ts (82,262 bytes)
2025-05-26 13:47:29,756 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts (53,380 bytes)
2025-05-26 13:47:29,772 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\dist\generated\ast-spec.d.ts (68,378 bytes)
2025-05-26 13:47:29,774 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\_ts3.4\dist\generated\ast-spec.d.ts (70,137 bytes)
2025-05-26 13:47:29,932 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts (816,786 bytes)
2025-05-26 13:47:29,932 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.es5.d.ts (213,196 bytes)
2025-05-26 13:47:29,932 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts (272,256 bytes)
2025-05-26 13:47:29,932 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\protocol.d.ts (107,934 bytes)
2025-05-26 13:47:29,946 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts (747,710 bytes)
2025-05-26 13:47:29,946 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts (598,789 bytes)
2025-05-26 13:47:29,946 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts (598,776 bytes)
2025-05-26 13:47:29,950 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts (335,559 bytes)
2025-05-26 13:47:29,957 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/api
2025-05-26 13:47:29,967 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:47:29,967 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:47:29,967 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:47:29,967 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:47:29,967 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:47:29,967 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:47:29,971 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:47:29,971 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:47:29,971 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:47:29,972 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:47:29,972 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:47:29,973 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:47:29,973 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:47:29,974 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/kits
2025-05-26 13:47:29,976 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/hms/ets/component
2025-05-26 13:47:29,976 - ComprehensiveRealSystemTest - INFO - Searching in: comprehensive_dataset
2025-05-26 13:47:29,980 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\basic_ui\text_input.d.ts (62,924 bytes)
2025-05-26 13:47:29,981 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\container\swiper.d.ts (55,612 bytes)
2025-05-26 13:47:29,982 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:47:29,983 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:47:29,984 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:47:29,985 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:47:29,985 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:47:29,985 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\form\text_input.d.ts (62,924 bytes)
2025-05-26 13:47:29,991 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:47:29,991 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:47:29,992 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:47:29,992 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:47:29,993 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:47:29,993 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:47:29,993 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:47:29,993 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:47:29,993 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:47:29,997 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:47:29,998 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:47:29,998 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:47:29,999 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:47:30,003 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:47:30,003 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:47:30,004 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:47:30,004 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:47:30,004 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:47:30,004 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:47:30,004 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\navigation\navigation.d.ts (83,995 bytes)
2025-05-26 13:47:30,005 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:47:30,005 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:47:30,006 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:47:30,006 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:47:30,006 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:47:30,006 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:47:30,006 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:47:30,006 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:47:30,009 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:47:30,010 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:47:30,011 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:47:30,017 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\system\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:47:30,018 - ComprehensiveRealSystemTest - INFO - Found 30 large ArkTS files for testing
2025-05-26 13:47:30,019 - ComprehensiveRealSystemTest - INFO - Found 30 large files for testing
2025-05-26 13:47:30,019 - ComprehensiveRealSystemTest - INFO - Step 2: Testing parsing capabilities...
2025-05-26 13:47:30,019 - ComprehensiveRealSystemTest - INFO - === Testing Parsing Capabilities ===
2025-05-26 13:47:30,021 - ComprehensiveRealSystemTest - INFO - Parsing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:47:30,083 - ComprehensiveRealSystemTest - INFO - Parsed 196 symbols in 0.062s
2025-05-26 13:47:30,083 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'type': 1, 'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 1, 'union_type': 176, 'intersection_type': 1}
2025-05-26 13:47:30,083 - ComprehensiveRealSystemTest - INFO - Parsing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:47:30,150 - ComprehensiveRealSystemTest - INFO - Parsed 210 symbols in 0.067s
2025-05-26 13:47:30,150 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 2, 'union_type': 190, 'intersection_type': 1}
2025-05-26 13:47:30,150 - ComprehensiveRealSystemTest - INFO - Parsing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:47:30,329 - ComprehensiveRealSystemTest - INFO - Parsed 2293 symbols in 0.180s
2025-05-26 13:47:30,329 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 45, 'interface': 1244, 'type': 185, 'function': 50, 'namespace': 110, 'enum': 41, 'const': 445, 'reexport_all': 1, 'callback_type': 12, 'union_type': 145, 'intersection_type': 12, 'export_assignment': 3}
2025-05-26 13:47:30,329 - ComprehensiveRealSystemTest - INFO - Parsing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:47:30,383 - ComprehensiveRealSystemTest - INFO - Parsed 147 symbols in 0.054s
2025-05-26 13:47:30,383 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'const': 88, 'union_type': 57}
2025-05-26 13:47:30,383 - ComprehensiveRealSystemTest - INFO - Parsing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:47:30,538 - ComprehensiveRealSystemTest - INFO - Parsed 2009 symbols in 0.155s
2025-05-26 13:47:30,538 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 3}
2025-05-26 13:47:30,538 - ComprehensiveRealSystemTest - INFO - Parsing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:47:30,680 - ComprehensiveRealSystemTest - INFO - Parsed 2008 symbols in 0.142s
2025-05-26 13:47:30,680 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 2}
2025-05-26 13:47:30,680 - ComprehensiveRealSystemTest - INFO - Parsing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:47:30,800 - ComprehensiveRealSystemTest - INFO - Parsed 1940 symbols in 0.120s
2025-05-26 13:47:30,800 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 27, 'interface': 1106, 'type': 169, 'function': 36, 'namespace': 41, 'enum': 39, 'const': 363, 'reexport_all': 1, 'callback_type': 12, 'union_type': 133, 'intersection_type': 11, 'export_assignment': 2}
2025-05-26 13:47:30,800 - ComprehensiveRealSystemTest - INFO - Parsing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 13:47:30,884 - ComprehensiveRealSystemTest - INFO - Parsed 243 symbols in 0.084s
2025-05-26 13:47:30,884 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2}
2025-05-26 13:47:30,884 - ComprehensiveRealSystemTest - INFO - Parsing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 13:47:30,974 - ComprehensiveRealSystemTest - INFO - Parsed 244 symbols in 0.090s
2025-05-26 13:47:30,974 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2, 'component': 1}
2025-05-26 13:47:30,975 - ComprehensiveRealSystemTest - INFO - Parsing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:47:31,072 - ComprehensiveRealSystemTest - INFO - Parsed 1663 symbols in 0.095s
2025-05-26 13:47:31,072 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 2}
2025-05-26 13:47:31,072 - ComprehensiveRealSystemTest - INFO - Parsing file 11/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:47:31,174 - ComprehensiveRealSystemTest - INFO - Parsed 1662 symbols in 0.101s
2025-05-26 13:47:31,174 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 1}
2025-05-26 13:47:31,175 - ComprehensiveRealSystemTest - INFO - Parsing file 12/30: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-26 13:47:31,209 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.033s
2025-05-26 13:47:31,209 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:47:31,209 - ComprehensiveRealSystemTest - INFO - Parsing file 13/30: comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-26 13:47:31,243 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.034s
2025-05-26 13:47:31,243 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:47:31,243 - ComprehensiveRealSystemTest - INFO - Parsing file 14/30: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-26 13:47:31,299 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.056s
2025-05-26 13:47:31,299 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:47:31,301 - ComprehensiveRealSystemTest - INFO - Parsing file 15/30: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-26 13:47:31,355 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.055s
2025-05-26 13:47:31,355 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:47:31,355 - ComprehensiveRealSystemTest - INFO - Parsing file 16/30: Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-26 13:47:31,443 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.087s
2025-05-26 13:47:31,444 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:47:31,444 - ComprehensiveRealSystemTest - INFO - Parsing file 17/30: comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-26 13:47:31,529 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.084s
2025-05-26 13:47:31,529 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:47:31,529 - ComprehensiveRealSystemTest - INFO - Parsing file 18/30: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-26 13:47:31,579 - ComprehensiveRealSystemTest - INFO - Parsed 67 symbols in 0.050s
2025-05-26 13:47:31,579 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 6, 'interface': 59, 'export_assignment': 1}
2025-05-26 13:47:31,580 - ComprehensiveRealSystemTest - INFO - Parsing file 19/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-26 13:47:31,638 - ComprehensiveRealSystemTest - INFO - Parsed 570 symbols in 0.057s
2025-05-26 13:47:31,638 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 122, 'interface': 263, 'type': 1, 'namespace': 65, 'const': 26, 'callback_type': 1, 'union_type': 55, 'intersection_type': 36, 'export_assignment': 1}
2025-05-26 13:47:31,638 - ComprehensiveRealSystemTest - INFO - Parsing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-26 13:47:31,690 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.052s
2025-05-26 13:47:31,690 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:47:31,690 - ComprehensiveRealSystemTest - INFO - Parsing file 21/30: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-26 13:47:31,737 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.047s
2025-05-26 13:47:31,737 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:47:31,737 - ComprehensiveRealSystemTest - INFO - Parsing file 22/30: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-26 13:47:31,787 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.050s
2025-05-26 13:47:31,787 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:47:31,787 - ComprehensiveRealSystemTest - INFO - Parsing file 23/30: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-26 13:47:31,829 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.042s
2025-05-26 13:47:31,829 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:47:31,829 - ComprehensiveRealSystemTest - INFO - Parsing file 24/30: Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-26 13:47:31,876 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.047s
2025-05-26 13:47:31,876 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 44, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:47:31,876 - ComprehensiveRealSystemTest - INFO - Parsing file 25/30: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-26 13:47:31,920 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.043s
2025-05-26 13:47:31,920 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:47:31,920 - ComprehensiveRealSystemTest - INFO - Parsing file 26/30: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-26 13:47:31,959 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.039s
2025-05-26 13:47:31,959 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:47:31,959 - ComprehensiveRealSystemTest - INFO - Parsing file 27/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-26 13:47:31,976 - ComprehensiveRealSystemTest - INFO - Parsed 105 symbols in 0.017s
2025-05-26 13:47:31,976 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 14, 'union_type': 90}
2025-05-26 13:47:31,976 - ComprehensiveRealSystemTest - INFO - Parsing file 28/30: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-26 13:47:32,021 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.036s
2025-05-26 13:47:32,021 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:47:32,021 - ComprehensiveRealSystemTest - INFO - Parsing file 29/30: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-26 13:47:32,060 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.039s
2025-05-26 13:47:32,060 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:47:32,060 - ComprehensiveRealSystemTest - INFO - Parsing file 30/30: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-26 13:47:32,110 - ComprehensiveRealSystemTest - INFO - Parsed 60 symbols in 0.051s
2025-05-26 13:47:32,110 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 25, 'interface': 31, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:47:32,112 - ComprehensiveRealSystemTest - INFO - Parsing completed: 30/30 files successful
2025-05-26 13:47:32,112 - ComprehensiveRealSystemTest - INFO - Total symbols parsed: 14688
2025-05-26 13:47:32,112 - ComprehensiveRealSystemTest - INFO - Symbol types found: {'type': 1132, 'namespace': 604, 'interface': 7257, 'const': 3102, 'callback_type': 109, 'union_type': 1408, 'intersection_type': 87, 'class': 405, 'function': 234, 'enum': 312, 'reexport_all': 6, 'export_assignment': 31, 'component': 1}
2025-05-26 13:47:32,112 - ComprehensiveRealSystemTest - INFO - Step 3: Testing indexing capabilities...
2025-05-26 13:47:32,112 - ComprehensiveRealSystemTest - INFO - === Testing Indexing Capabilities ===
2025-05-26 13:47:32,651 - ComprehensiveRealSystemTest - INFO - Qdrant collection created successfully
2025-05-26 13:47:32,651 - ComprehensiveRealSystemTest - INFO - Indexing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:47:43,456 - ComprehensiveRealSystemTest - INFO - Indexed 196 symbols in 10.804s
2025-05-26 13:47:43,456 - ComprehensiveRealSystemTest - INFO - Indexing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:47:48,587 - ComprehensiveRealSystemTest - INFO - Indexed 210 symbols in 5.132s
2025-05-26 13:47:48,587 - ComprehensiveRealSystemTest - INFO - Indexing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:49:05,852 - ComprehensiveRealSystemTest - INFO - Indexed 2293 symbols in 77.265s
2025-05-26 13:49:05,852 - ComprehensiveRealSystemTest - INFO - Indexing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:49:12,478 - ComprehensiveRealSystemTest - INFO - Indexed 147 symbols in 6.626s
2025-05-26 13:49:12,478 - ComprehensiveRealSystemTest - INFO - Indexing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:49:24,594 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:49:24,594 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-26 13:49:24,594 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-26 13:49:24,595 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:0.6b
2025-05-26 13:49:24,595 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:49:24,595 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-26 13:49:24,595 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-26 13:49:24,595 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-26 13:49:24,595 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/api
2025-05-26 13:49:24,596 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.appAccount.d.ts (136,704 bytes)
2025-05-26 13:49:24,597 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.osAccount.d.ts (59,446 bytes)
2025-05-26 13:49:24,600 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.arkui.UIContext.d.ts (123,406 bytes)
2025-05-26 13:49:24,600 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:49:24,601 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:49:24,601 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:49:24,601 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.buffer.d.ts (190,085 bytes)
2025-05-26 13:49:24,601 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:49:24,602 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEvent.d.ts (65,130 bytes)
2025-05-26 13:49:24,602 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEventManager.d.ts (68,365 bytes)
2025-05-26 13:49:24,602 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.contact.d.ts (143,754 bytes)
2025-05-26 13:49:24,602 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:49:24,603 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:49:24,603 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:49:24,603 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:49:24,604 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.display.d.ts (50,958 bytes)
2025-05-26 13:49:24,605 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:49:24,605 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:49:24,606 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:49:24,606 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.geoLocationManager.d.ts (95,526 bytes)
2025-05-26 13:49:24,606 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts (149,907 bytes)
2025-05-26 13:49:24,607 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts (50,405 bytes)
2025-05-26 13:49:24,607 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.i18n.d.ts (111,611 bytes)
2025-05-26 13:49:24,607 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethod.d.ts (75,816 bytes)
2025-05-26 13:49:24,608 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethodEngine.d.ts (82,240 bytes)
2025-05-26 13:49:24,608 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.intl.d.ts (116,417 bytes)
2025-05-26 13:49:24,608 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:49:24,609 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:49:24,609 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:49:24,609 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:49:24,609 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:49:24,609 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:49:24,610 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts (51,152 bytes)
2025-05-26 13:49:24,610 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:49:24,610 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:49:24,611 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:49:24,611 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.nfc.tag.d.ts (60,146 bytes)
2025-05-26 13:49:24,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.notificationManager.d.ts (67,491 bytes)
2025-05-26 13:49:24,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.pasteboard.d.ts (57,975 bytes)
2025-05-26 13:49:24,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:49:24,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.resourceManager.d.ts (245,843 bytes)
2025-05-26 13:49:24,613 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.router.d.ts (50,688 bytes)
2025-05-26 13:49:24,613 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.rpc.d.ts (170,228 bytes)
2025-05-26 13:49:24,613 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cert.d.ts (214,222 bytes)
2025-05-26 13:49:24,613 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts (336,615 bytes)
2025-05-26 13:49:24,613 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.huks.d.ts (186,266 bytes)
2025-05-26 13:49:24,614 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:49:24,614 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.UiTest.d.ts (158,743 bytes)
2025-05-26 13:49:24,614 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.userIAM.userAuth.d.ts (55,963 bytes)
2025-05-26 13:49:24,615 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.util.d.ts (198,770 bytes)
2025-05-26 13:49:24,615 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.netErrorList.d.ts (61,804 bytes)
2025-05-26 13:49:24,616 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts (267,704 bytes)
2025-05-26 13:49:24,616 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:49:24,616 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.window.d.ts (286,762 bytes)
2025-05-26 13:49:24,616 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.worker.d.ts (86,613 bytes)
2025-05-26 13:49:24,617 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.zlib.d.ts (98,263 bytes)
2025-05-26 13:49:24,619 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\AbilityDelegator.d.ts (66,895 bytes)
2025-05-26 13:49:24,620 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\UIAbilityContext.d.ts (109,096 bytes)
2025-05-26 13:49:24,624 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\tag\nfctech.d.ts (69,043 bytes)
2025-05-26 13:49:24,625 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/component
2025-05-26 13:49:24,626 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\canvas.d.ts (209,139 bytes)
2025-05-26 13:49:24,626 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common.d.ts (566,343 bytes)
2025-05-26 13:49:24,626 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\enums.d.ts (205,914 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\gesture.d.ts (97,133 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\navigation.d.ts (83,995 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\swiper.d.ts (55,612 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\text_input.d.ts (62,924 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\units.d.ts (68,870 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\web.d.ts (231,955 bytes)
2025-05-26 13:49:24,627 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/build-tools
2025-05-26 13:49:24,632 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\aot\src\lib_ark_builtins.d.ts (54,703 bytes)
2025-05-26 13:49:24,633 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts (842,681 bytes)
2025-05-26 13:49:24,635 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.es5.d.ts (212,312 bytes)
2025-05-26 13:49:24,635 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.webworker.d.ts (259,617 bytes)
2025-05-26 13:49:24,635 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\protocol.d.ts (98,216 bytes)
2025-05-26 13:49:24,635 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts (587,234 bytes)
2025-05-26 13:49:24,636 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts (447,512 bytes)
2025-05-26 13:49:24,636 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts (447,499 bytes)
2025-05-26 13:49:24,643 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\canvas.d.ts (209,151 bytes)
2025-05-26 13:49:24,644 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts (566,649 bytes)
2025-05-26 13:49:24,644 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:49:24,645 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\enums.d.ts (205,914 bytes)
2025-05-26 13:49:24,645 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\gesture.d.ts (97,133 bytes)
2025-05-26 13:49:24,646 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\navigation.d.ts (84,079 bytes)
2025-05-26 13:49:24,647 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:49:24,647 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\swiper.d.ts (55,612 bytes)
2025-05-26 13:49:24,648 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\text_input.d.ts (62,924 bytes)
2025-05-26 13:49:24,648 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\units.d.ts (68,936 bytes)
2025-05-26 13:49:24,648 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\web.d.ts (231,967 bytes)
2025-05-26 13:49:24,668 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index-legacy.d.ts (168,429 bytes)
2025-05-26 13:49:24,668 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts (611,184 bytes)
2025-05-26 13:49:24,682 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts (52,155 bytes)
2025-05-26 13:49:24,682 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts (52,150 bytes)
2025-05-26 13:49:24,690 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\buffer.d.ts (107,224 bytes)
2025-05-26 13:49:24,690 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\child_process.d.ts (67,998 bytes)
2025-05-26 13:49:24,690 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\crypto.d.ts (183,978 bytes)
2025-05-26 13:49:24,691 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs.d.ts (182,973 bytes)
2025-05-26 13:49:24,691 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http.d.ts (79,056 bytes)
2025-05-26 13:49:24,691 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http2.d.ts (113,815 bytes)
2025-05-26 13:49:24,691 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\inspector.d.ts (125,369 bytes)
2025-05-26 13:49:24,692 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\process.d.ts (73,684 bytes)
2025-05-26 13:49:24,692 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\stream.d.ts (70,767 bytes)
2025-05-26 13:49:24,693 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\tls.d.ts (54,923 bytes)
2025-05-26 13:49:24,693 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\util.d.ts (82,275 bytes)
2025-05-26 13:49:24,694 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts (53,450 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\buffer.d.ts (107,224 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\child_process.d.ts (67,998 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\crypto.d.ts (183,913 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs.d.ts (182,973 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http.d.ts (79,056 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http2.d.ts (113,815 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\inspector.d.ts (125,369 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\process.d.ts (73,684 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\stream.d.ts (72,452 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\tls.d.ts (54,923 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\util.d.ts (82,262 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts (53,380 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\dist\generated\ast-spec.d.ts (68,378 bytes)
2025-05-26 13:49:24,695 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\_ts3.4\dist\generated\ast-spec.d.ts (70,137 bytes)
2025-05-26 13:49:24,799 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts (816,786 bytes)
2025-05-26 13:49:24,799 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.es5.d.ts (213,196 bytes)
2025-05-26 13:49:24,816 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts (272,256 bytes)
2025-05-26 13:49:24,816 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\protocol.d.ts (107,934 bytes)
2025-05-26 13:49:24,816 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts (747,710 bytes)
2025-05-26 13:49:24,816 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts (598,789 bytes)
2025-05-26 13:49:24,816 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts (598,776 bytes)
2025-05-26 13:49:24,819 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts (335,559 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/api
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:49:24,827 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:49:24,835 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:49:24,835 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:49:24,835 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:49:24,837 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/kits
2025-05-26 13:49:24,838 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/hms/ets/component
2025-05-26 13:49:24,839 - ComprehensiveRealSystemTest - INFO - Searching in: comprehensive_dataset
2025-05-26 13:49:24,840 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\basic_ui\text_input.d.ts (62,924 bytes)
2025-05-26 13:49:24,841 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\container\swiper.d.ts (55,612 bytes)
2025-05-26 13:49:24,841 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:49:24,841 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:49:24,842 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:49:24,842 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:49:24,842 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:49:24,843 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\form\text_input.d.ts (62,924 bytes)
2025-05-26 13:49:24,846 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:49:24,847 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:49:24,847 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:49:24,847 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:49:24,848 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:49:24,848 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:49:24,848 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:49:24,848 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:49:24,848 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:49:24,849 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:49:24,849 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:49:24,850 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:49:24,850 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:49:24,851 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:49:24,851 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\navigation\navigation.d.ts (83,995 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:49:24,852 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:49:24,859 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\system\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:49:24,859 - ComprehensiveRealSystemTest - INFO - Found 30 large ArkTS files for testing
2025-05-26 13:49:24,859 - ComprehensiveRealSystemTest - INFO - Found 30 large files for testing
2025-05-26 13:49:24,860 - ComprehensiveRealSystemTest - INFO - Step 2: Testing parsing capabilities...
2025-05-26 13:49:24,860 - ComprehensiveRealSystemTest - INFO - === Testing Parsing Capabilities ===
2025-05-26 13:49:24,863 - ComprehensiveRealSystemTest - INFO - Parsing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:49:24,930 - ComprehensiveRealSystemTest - INFO - Parsed 196 symbols in 0.066s
2025-05-26 13:49:24,930 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'type': 1, 'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 1, 'union_type': 176, 'intersection_type': 1}
2025-05-26 13:49:24,931 - ComprehensiveRealSystemTest - INFO - Parsing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:49:24,995 - ComprehensiveRealSystemTest - INFO - Parsed 210 symbols in 0.064s
2025-05-26 13:49:24,995 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 2, 'union_type': 190, 'intersection_type': 1}
2025-05-26 13:49:24,995 - ComprehensiveRealSystemTest - INFO - Parsing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:49:25,165 - ComprehensiveRealSystemTest - INFO - Parsed 2293 symbols in 0.170s
2025-05-26 13:49:25,165 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 45, 'interface': 1244, 'type': 185, 'function': 50, 'namespace': 110, 'enum': 41, 'const': 445, 'reexport_all': 1, 'callback_type': 12, 'union_type': 145, 'intersection_type': 12, 'export_assignment': 3}
2025-05-26 13:49:25,165 - ComprehensiveRealSystemTest - INFO - Parsing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:49:25,228 - ComprehensiveRealSystemTest - INFO - Parsed 147 symbols in 0.064s
2025-05-26 13:49:25,228 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'const': 88, 'union_type': 57}
2025-05-26 13:49:25,228 - ComprehensiveRealSystemTest - INFO - Parsing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:49:25,380 - ComprehensiveRealSystemTest - INFO - Parsed 2009 symbols in 0.152s
2025-05-26 13:49:25,380 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 3}
2025-05-26 13:49:25,380 - ComprehensiveRealSystemTest - INFO - Parsing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:49:25,533 - ComprehensiveRealSystemTest - INFO - Parsed 2008 symbols in 0.152s
2025-05-26 13:49:25,533 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 2}
2025-05-26 13:49:25,533 - ComprehensiveRealSystemTest - INFO - Parsing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:49:25,651 - ComprehensiveRealSystemTest - INFO - Parsed 1940 symbols in 0.118s
2025-05-26 13:49:25,651 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 27, 'interface': 1106, 'type': 169, 'function': 36, 'namespace': 41, 'enum': 39, 'const': 363, 'reexport_all': 1, 'callback_type': 12, 'union_type': 133, 'intersection_type': 11, 'export_assignment': 2}
2025-05-26 13:49:25,651 - ComprehensiveRealSystemTest - INFO - Parsing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 13:49:25,744 - ComprehensiveRealSystemTest - INFO - Parsed 243 symbols in 0.093s
2025-05-26 13:49:25,744 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2}
2025-05-26 13:49:25,744 - ComprehensiveRealSystemTest - INFO - Parsing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 13:49:25,821 - ComprehensiveRealSystemTest - INFO - Parsed 244 symbols in 0.077s
2025-05-26 13:49:25,821 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2, 'component': 1}
2025-05-26 13:49:25,821 - ComprehensiveRealSystemTest - INFO - Parsing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:49:25,919 - ComprehensiveRealSystemTest - INFO - Parsed 1663 symbols in 0.098s
2025-05-26 13:49:25,919 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 2}
2025-05-26 13:49:25,919 - ComprehensiveRealSystemTest - INFO - Parsing file 11/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:49:26,022 - ComprehensiveRealSystemTest - INFO - Parsed 1662 symbols in 0.103s
2025-05-26 13:49:26,022 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 1}
2025-05-26 13:49:26,022 - ComprehensiveRealSystemTest - INFO - Parsing file 12/30: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-26 13:49:26,054 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.031s
2025-05-26 13:49:26,054 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:49:26,054 - ComprehensiveRealSystemTest - INFO - Parsing file 13/30: comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-26 13:49:26,085 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.031s
2025-05-26 13:49:26,085 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:49:26,085 - ComprehensiveRealSystemTest - INFO - Parsing file 14/30: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-26 13:49:26,151 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.066s
2025-05-26 13:49:26,152 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:49:26,152 - ComprehensiveRealSystemTest - INFO - Parsing file 15/30: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-26 13:49:26,198 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.046s
2025-05-26 13:49:26,198 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:49:26,198 - ComprehensiveRealSystemTest - INFO - Parsing file 16/30: Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-26 13:49:26,286 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.088s
2025-05-26 13:49:26,286 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:49:26,286 - ComprehensiveRealSystemTest - INFO - Parsing file 17/30: comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-26 13:49:26,370 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.084s
2025-05-26 13:49:26,370 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:49:26,370 - ComprehensiveRealSystemTest - INFO - Parsing file 18/30: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-26 13:49:26,431 - ComprehensiveRealSystemTest - INFO - Parsed 67 symbols in 0.061s
2025-05-26 13:49:26,431 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 6, 'interface': 59, 'export_assignment': 1}
2025-05-26 13:49:26,431 - ComprehensiveRealSystemTest - INFO - Parsing file 19/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-26 13:49:26,481 - ComprehensiveRealSystemTest - INFO - Parsed 570 symbols in 0.050s
2025-05-26 13:49:26,481 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 122, 'interface': 263, 'type': 1, 'namespace': 65, 'const': 26, 'callback_type': 1, 'union_type': 55, 'intersection_type': 36, 'export_assignment': 1}
2025-05-26 13:49:26,481 - ComprehensiveRealSystemTest - INFO - Parsing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-26 13:49:26,531 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.051s
2025-05-26 13:49:26,531 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:49:26,531 - ComprehensiveRealSystemTest - INFO - Parsing file 21/30: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-26 13:49:26,591 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.059s
2025-05-26 13:49:26,591 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:49:26,591 - ComprehensiveRealSystemTest - INFO - Parsing file 22/30: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-26 13:49:26,637 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.046s
2025-05-26 13:49:26,637 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:49:26,637 - ComprehensiveRealSystemTest - INFO - Parsing file 23/30: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-26 13:49:26,681 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.045s
2025-05-26 13:49:26,681 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:49:26,681 - ComprehensiveRealSystemTest - INFO - Parsing file 24/30: Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-26 13:49:26,728 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.047s
2025-05-26 13:49:26,729 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 44, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:49:26,729 - ComprehensiveRealSystemTest - INFO - Parsing file 25/30: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-26 13:49:26,770 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.040s
2025-05-26 13:49:26,771 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:49:26,771 - ComprehensiveRealSystemTest - INFO - Parsing file 26/30: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-26 13:49:26,804 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.033s
2025-05-26 13:49:26,804 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:49:26,804 - ComprehensiveRealSystemTest - INFO - Parsing file 27/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-26 13:49:26,834 - ComprehensiveRealSystemTest - INFO - Parsed 105 symbols in 0.030s
2025-05-26 13:49:26,835 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 14, 'union_type': 90}
2025-05-26 13:49:26,835 - ComprehensiveRealSystemTest - INFO - Parsing file 28/30: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-26 13:49:26,874 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.039s
2025-05-26 13:49:26,874 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:49:26,874 - ComprehensiveRealSystemTest - INFO - Parsing file 29/30: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-26 13:49:26,915 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.041s
2025-05-26 13:49:26,915 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:49:26,915 - ComprehensiveRealSystemTest - INFO - Parsing file 30/30: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-26 13:49:26,954 - ComprehensiveRealSystemTest - INFO - Parsed 60 symbols in 0.038s
2025-05-26 13:49:26,954 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 25, 'interface': 31, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:49:26,954 - ComprehensiveRealSystemTest - INFO - Parsing completed: 30/30 files successful
2025-05-26 13:49:26,954 - ComprehensiveRealSystemTest - INFO - Total symbols parsed: 14688
2025-05-26 13:49:26,954 - ComprehensiveRealSystemTest - INFO - Symbol types found: {'type': 1132, 'namespace': 604, 'interface': 7257, 'const': 3102, 'callback_type': 109, 'union_type': 1408, 'intersection_type': 87, 'class': 405, 'function': 234, 'enum': 312, 'reexport_all': 6, 'export_assignment': 31, 'component': 1}
2025-05-26 13:49:26,954 - ComprehensiveRealSystemTest - INFO - Step 3: Testing indexing capabilities...
2025-05-26 13:49:26,954 - ComprehensiveRealSystemTest - INFO - === Testing Indexing Capabilities ===
2025-05-26 13:49:28,452 - ComprehensiveRealSystemTest - INFO - Qdrant collection created successfully
2025-05-26 13:49:28,452 - ComprehensiveRealSystemTest - INFO - Indexing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:49:35,979 - ComprehensiveRealSystemTest - INFO - Indexed 196 symbols in 7.527s
2025-05-26 13:49:35,979 - ComprehensiveRealSystemTest - INFO - Indexing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:49:42,820 - ComprehensiveRealSystemTest - INFO - Indexed 210 symbols in 6.841s
2025-05-26 13:49:42,820 - ComprehensiveRealSystemTest - INFO - Indexing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:0.6b
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/api
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.appAccount.d.ts (136,704 bytes)
2025-05-26 13:50:02,066 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.osAccount.d.ts (59,446 bytes)
2025-05-26 13:50:02,081 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.arkui.UIContext.d.ts (123,406 bytes)
2025-05-26 13:50:02,081 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:50:02,084 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:50:02,085 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:50:02,085 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.buffer.d.ts (190,085 bytes)
2025-05-26 13:50:02,085 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:50:02,085 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEvent.d.ts (65,130 bytes)
2025-05-26 13:50:02,086 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEventManager.d.ts (68,365 bytes)
2025-05-26 13:50:02,086 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.contact.d.ts (143,754 bytes)
2025-05-26 13:50:02,086 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:50:02,086 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:50:02,086 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:50:02,087 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:50:02,087 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.display.d.ts (50,958 bytes)
2025-05-26 13:50:02,088 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:50:02,088 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:50:02,089 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:50:02,089 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.geoLocationManager.d.ts (95,526 bytes)
2025-05-26 13:50:02,089 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts (149,907 bytes)
2025-05-26 13:50:02,089 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts (50,405 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.i18n.d.ts (111,611 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethod.d.ts (75,816 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethodEngine.d.ts (82,240 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.intl.d.ts (116,417 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts (51,152 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:50:02,090 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.nfc.tag.d.ts (60,146 bytes)
2025-05-26 13:50:02,094 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.notificationManager.d.ts (67,491 bytes)
2025-05-26 13:50:02,095 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.pasteboard.d.ts (57,975 bytes)
2025-05-26 13:50:02,095 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:50:02,095 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.resourceManager.d.ts (245,843 bytes)
2025-05-26 13:50:02,096 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.router.d.ts (50,688 bytes)
2025-05-26 13:50:02,096 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.rpc.d.ts (170,228 bytes)
2025-05-26 13:50:02,096 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cert.d.ts (214,222 bytes)
2025-05-26 13:50:02,096 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts (336,615 bytes)
2025-05-26 13:50:02,096 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.huks.d.ts (186,266 bytes)
2025-05-26 13:50:02,097 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:50:02,097 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.UiTest.d.ts (158,743 bytes)
2025-05-26 13:50:02,097 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.userIAM.userAuth.d.ts (55,963 bytes)
2025-05-26 13:50:02,098 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.util.d.ts (198,770 bytes)
2025-05-26 13:50:02,098 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.netErrorList.d.ts (61,804 bytes)
2025-05-26 13:50:02,099 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts (267,704 bytes)
2025-05-26 13:50:02,099 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:50:02,099 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.window.d.ts (286,762 bytes)
2025-05-26 13:50:02,099 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.worker.d.ts (86,613 bytes)
2025-05-26 13:50:02,099 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.zlib.d.ts (98,263 bytes)
2025-05-26 13:50:02,101 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\AbilityDelegator.d.ts (66,895 bytes)
2025-05-26 13:50:02,102 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\UIAbilityContext.d.ts (109,096 bytes)
2025-05-26 13:50:02,106 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\tag\nfctech.d.ts (69,043 bytes)
2025-05-26 13:50:02,107 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/component
2025-05-26 13:50:02,107 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\canvas.d.ts (209,139 bytes)
2025-05-26 13:50:02,108 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common.d.ts (566,343 bytes)
2025-05-26 13:50:02,109 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:50:02,111 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\enums.d.ts (205,914 bytes)
2025-05-26 13:50:02,111 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\gesture.d.ts (97,133 bytes)
2025-05-26 13:50:02,112 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\navigation.d.ts (83,995 bytes)
2025-05-26 13:50:02,113 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:50:02,114 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\swiper.d.ts (55,612 bytes)
2025-05-26 13:50:02,114 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\text_input.d.ts (62,924 bytes)
2025-05-26 13:50:02,115 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\units.d.ts (68,870 bytes)
2025-05-26 13:50:02,115 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\web.d.ts (231,955 bytes)
2025-05-26 13:50:02,115 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/build-tools
2025-05-26 13:50:02,115 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\aot\src\lib_ark_builtins.d.ts (54,703 bytes)
2025-05-26 13:50:02,118 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts (842,681 bytes)
2025-05-26 13:50:02,119 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.es5.d.ts (212,312 bytes)
2025-05-26 13:50:02,120 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.webworker.d.ts (259,617 bytes)
2025-05-26 13:50:02,120 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\protocol.d.ts (98,216 bytes)
2025-05-26 13:50:02,120 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts (587,234 bytes)
2025-05-26 13:50:02,120 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts (447,512 bytes)
2025-05-26 13:50:02,120 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts (447,499 bytes)
2025-05-26 13:50:02,124 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\canvas.d.ts (209,151 bytes)
2025-05-26 13:50:02,124 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts (566,649 bytes)
2025-05-26 13:50:02,124 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:50:02,129 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\enums.d.ts (205,914 bytes)
2025-05-26 13:50:02,129 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\gesture.d.ts (97,133 bytes)
2025-05-26 13:50:02,130 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\navigation.d.ts (84,079 bytes)
2025-05-26 13:50:02,131 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:50:02,131 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\swiper.d.ts (55,612 bytes)
2025-05-26 13:50:02,132 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\text_input.d.ts (62,924 bytes)
2025-05-26 13:50:02,132 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\units.d.ts (68,936 bytes)
2025-05-26 13:50:02,132 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\web.d.ts (231,967 bytes)
2025-05-26 13:50:02,156 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index-legacy.d.ts (168,429 bytes)
2025-05-26 13:50:02,156 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts (611,184 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts (52,155 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts (52,150 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\buffer.d.ts (107,224 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\child_process.d.ts (67,998 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\crypto.d.ts (183,978 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs.d.ts (182,973 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http.d.ts (79,056 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http2.d.ts (113,815 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\inspector.d.ts (125,369 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\process.d.ts (73,684 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\stream.d.ts (70,767 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\tls.d.ts (54,923 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\util.d.ts (82,275 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts (53,450 bytes)
2025-05-26 13:50:02,174 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\buffer.d.ts (107,224 bytes)
2025-05-26 13:50:02,182 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\child_process.d.ts (67,998 bytes)
2025-05-26 13:50:02,182 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\crypto.d.ts (183,913 bytes)
2025-05-26 13:50:02,182 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs.d.ts (182,973 bytes)
2025-05-26 13:50:02,183 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http.d.ts (79,056 bytes)
2025-05-26 13:50:02,183 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http2.d.ts (113,815 bytes)
2025-05-26 13:50:02,183 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\inspector.d.ts (125,369 bytes)
2025-05-26 13:50:02,183 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\process.d.ts (73,684 bytes)
2025-05-26 13:50:02,183 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\stream.d.ts (72,452 bytes)
2025-05-26 13:50:02,185 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\tls.d.ts (54,923 bytes)
2025-05-26 13:50:02,185 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\util.d.ts (82,262 bytes)
2025-05-26 13:50:02,186 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts (53,380 bytes)
2025-05-26 13:50:02,187 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\dist\generated\ast-spec.d.ts (68,378 bytes)
2025-05-26 13:50:02,193 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\_ts3.4\dist\generated\ast-spec.d.ts (70,137 bytes)
2025-05-26 13:50:02,285 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts (816,786 bytes)
2025-05-26 13:50:02,285 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.es5.d.ts (213,196 bytes)
2025-05-26 13:50:02,285 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts (272,256 bytes)
2025-05-26 13:50:02,285 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\protocol.d.ts (107,934 bytes)
2025-05-26 13:50:02,285 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts (747,710 bytes)
2025-05-26 13:50:02,285 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts (598,789 bytes)
2025-05-26 13:50:02,285 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts (598,776 bytes)
2025-05-26 13:50:02,285 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts (335,559 bytes)
2025-05-26 13:50:02,307 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/api
2025-05-26 13:50:02,307 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:50:02,307 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:50:02,307 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:50:02,307 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:50:02,316 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:50:02,316 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:50:02,316 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:50:02,316 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:50:02,316 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:50:02,318 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:50:02,319 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:50:02,319 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:50:02,319 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:50:02,320 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/kits
2025-05-26 13:50:02,321 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/hms/ets/component
2025-05-26 13:50:02,322 - ComprehensiveRealSystemTest - INFO - Searching in: comprehensive_dataset
2025-05-26 13:50:02,323 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\basic_ui\text_input.d.ts (62,924 bytes)
2025-05-26 13:50:02,323 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\container\swiper.d.ts (55,612 bytes)
2025-05-26 13:50:02,324 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:50:02,325 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:50:02,325 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:50:02,325 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:50:02,326 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:50:02,326 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\form\text_input.d.ts (62,924 bytes)
2025-05-26 13:50:02,329 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:50:02,329 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:50:02,330 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:50:02,330 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:50:02,330 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:50:02,330 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:50:02,331 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:50:02,331 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:50:02,331 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:50:02,332 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:50:02,332 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:50:02,332 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:50:02,332 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:50:02,333 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:50:02,333 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:50:02,334 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:50:02,334 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:50:02,334 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:50:02,334 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:50:02,334 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\navigation\navigation.d.ts (83,995 bytes)
2025-05-26 13:50:02,334 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:50:02,336 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:50:02,336 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:50:02,336 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:50:02,337 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:50:02,337 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:50:02,337 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:50:02,337 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:50:02,338 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:50:02,338 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:50:02,338 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:50:02,340 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\system\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:50:02,341 - ComprehensiveRealSystemTest - INFO - Found 30 large ArkTS files for testing
2025-05-26 13:50:02,341 - ComprehensiveRealSystemTest - INFO - Found 30 large files for testing
2025-05-26 13:50:02,341 - ComprehensiveRealSystemTest - INFO - Step 2: Testing parsing capabilities...
2025-05-26 13:50:02,342 - ComprehensiveRealSystemTest - INFO - === Testing Parsing Capabilities ===
2025-05-26 13:50:02,343 - ComprehensiveRealSystemTest - INFO - Parsing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:50:02,412 - ComprehensiveRealSystemTest - INFO - Parsed 196 symbols in 0.069s
2025-05-26 13:50:02,412 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'type': 1, 'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 1, 'union_type': 176, 'intersection_type': 1}
2025-05-26 13:50:02,412 - ComprehensiveRealSystemTest - INFO - Parsing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:50:02,469 - ComprehensiveRealSystemTest - INFO - Parsed 210 symbols in 0.057s
2025-05-26 13:50:02,469 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 2, 'union_type': 190, 'intersection_type': 1}
2025-05-26 13:50:02,469 - ComprehensiveRealSystemTest - INFO - Parsing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:50:02,659 - ComprehensiveRealSystemTest - INFO - Parsed 2293 symbols in 0.190s
2025-05-26 13:50:02,659 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 45, 'interface': 1244, 'type': 185, 'function': 50, 'namespace': 110, 'enum': 41, 'const': 445, 'reexport_all': 1, 'callback_type': 12, 'union_type': 145, 'intersection_type': 12, 'export_assignment': 3}
2025-05-26 13:50:02,659 - ComprehensiveRealSystemTest - INFO - Parsing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:50:02,704 - ComprehensiveRealSystemTest - INFO - Parsed 147 symbols in 0.046s
2025-05-26 13:50:02,720 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'const': 88, 'union_type': 57}
2025-05-26 13:50:02,720 - ComprehensiveRealSystemTest - INFO - Parsing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:50:02,863 - ComprehensiveRealSystemTest - INFO - Parsed 2009 symbols in 0.143s
2025-05-26 13:50:02,863 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 3}
2025-05-26 13:50:02,863 - ComprehensiveRealSystemTest - INFO - Parsing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:50:03,025 - ComprehensiveRealSystemTest - INFO - Parsed 2008 symbols in 0.161s
2025-05-26 13:50:03,026 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 2}
2025-05-26 13:50:03,026 - ComprehensiveRealSystemTest - INFO - Parsing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:50:03,151 - ComprehensiveRealSystemTest - INFO - Parsed 1940 symbols in 0.124s
2025-05-26 13:50:03,151 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 27, 'interface': 1106, 'type': 169, 'function': 36, 'namespace': 41, 'enum': 39, 'const': 363, 'reexport_all': 1, 'callback_type': 12, 'union_type': 133, 'intersection_type': 11, 'export_assignment': 2}
2025-05-26 13:50:03,151 - ComprehensiveRealSystemTest - INFO - Parsing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 13:50:03,220 - ComprehensiveRealSystemTest - INFO - Parsed 243 symbols in 0.069s
2025-05-26 13:50:03,220 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2}
2025-05-26 13:50:03,220 - ComprehensiveRealSystemTest - INFO - Parsing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 13:50:03,306 - ComprehensiveRealSystemTest - INFO - Parsed 244 symbols in 0.086s
2025-05-26 13:50:03,306 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2, 'component': 1}
2025-05-26 13:50:03,306 - ComprehensiveRealSystemTest - INFO - Parsing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:50:03,400 - ComprehensiveRealSystemTest - INFO - Parsed 1663 symbols in 0.095s
2025-05-26 13:50:03,411 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 2}
2025-05-26 13:50:03,411 - ComprehensiveRealSystemTest - INFO - Parsing file 11/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:50:03,503 - ComprehensiveRealSystemTest - INFO - Parsed 1662 symbols in 0.093s
2025-05-26 13:50:03,503 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 1}
2025-05-26 13:50:03,503 - ComprehensiveRealSystemTest - INFO - Parsing file 12/30: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-26 13:50:03,547 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.044s
2025-05-26 13:50:03,548 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:50:03,548 - ComprehensiveRealSystemTest - INFO - Parsing file 13/30: comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-26 13:50:03,580 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.032s
2025-05-26 13:50:03,580 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:50:03,580 - ComprehensiveRealSystemTest - INFO - Parsing file 14/30: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-26 13:50:03,636 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.055s
2025-05-26 13:50:03,636 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:50:03,636 - ComprehensiveRealSystemTest - INFO - Parsing file 15/30: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-26 13:50:03,686 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.050s
2025-05-26 13:50:03,686 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:50:03,686 - ComprehensiveRealSystemTest - INFO - Parsing file 16/30: Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-26 13:50:03,779 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.093s
2025-05-26 13:50:03,780 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:50:03,780 - ComprehensiveRealSystemTest - INFO - Parsing file 17/30: comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-26 13:50:03,863 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.083s
2025-05-26 13:50:03,863 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:50:03,863 - ComprehensiveRealSystemTest - INFO - Parsing file 18/30: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-26 13:50:03,912 - ComprehensiveRealSystemTest - INFO - Parsed 67 symbols in 0.048s
2025-05-26 13:50:03,912 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 6, 'interface': 59, 'export_assignment': 1}
2025-05-26 13:50:03,912 - ComprehensiveRealSystemTest - INFO - Parsing file 19/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-26 13:50:03,964 - ComprehensiveRealSystemTest - INFO - Parsed 570 symbols in 0.053s
2025-05-26 13:50:03,964 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 122, 'interface': 263, 'type': 1, 'namespace': 65, 'const': 26, 'callback_type': 1, 'union_type': 55, 'intersection_type': 36, 'export_assignment': 1}
2025-05-26 13:50:03,964 - ComprehensiveRealSystemTest - INFO - Parsing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-26 13:50:04,025 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.060s
2025-05-26 13:50:04,025 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:50:04,025 - ComprehensiveRealSystemTest - INFO - Parsing file 21/30: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-26 13:50:04,075 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.050s
2025-05-26 13:50:04,075 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:50:04,075 - ComprehensiveRealSystemTest - INFO - Parsing file 22/30: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-26 13:50:04,125 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.050s
2025-05-26 13:50:04,125 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:50:04,125 - ComprehensiveRealSystemTest - INFO - Parsing file 23/30: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-26 13:50:04,174 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.049s
2025-05-26 13:50:04,174 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:50:04,174 - ComprehensiveRealSystemTest - INFO - Parsing file 24/30: Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-26 13:50:04,213 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.040s
2025-05-26 13:50:04,213 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 44, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:50:04,213 - ComprehensiveRealSystemTest - INFO - Parsing file 25/30: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-26 13:50:04,258 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.045s
2025-05-26 13:50:04,258 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:50:04,258 - ComprehensiveRealSystemTest - INFO - Parsing file 26/30: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-26 13:50:04,296 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.038s
2025-05-26 13:50:04,296 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:50:04,296 - ComprehensiveRealSystemTest - INFO - Parsing file 27/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-26 13:50:04,325 - ComprehensiveRealSystemTest - INFO - Parsed 105 symbols in 0.029s
2025-05-26 13:50:04,325 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 14, 'union_type': 90}
2025-05-26 13:50:04,325 - ComprehensiveRealSystemTest - INFO - Parsing file 28/30: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-26 13:50:04,362 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.036s
2025-05-26 13:50:04,362 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:50:04,362 - ComprehensiveRealSystemTest - INFO - Parsing file 29/30: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-26 13:50:04,411 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.047s
2025-05-26 13:50:04,413 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:50:04,413 - ComprehensiveRealSystemTest - INFO - Parsing file 30/30: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-26 13:50:04,467 - ComprehensiveRealSystemTest - INFO - Parsed 60 symbols in 0.052s
2025-05-26 13:50:04,467 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 25, 'interface': 31, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:50:04,469 - ComprehensiveRealSystemTest - INFO - Parsing completed: 30/30 files successful
2025-05-26 13:50:04,469 - ComprehensiveRealSystemTest - INFO - Total symbols parsed: 14688
2025-05-26 13:50:04,469 - ComprehensiveRealSystemTest - INFO - Symbol types found: {'type': 1132, 'namespace': 604, 'interface': 7257, 'const': 3102, 'callback_type': 109, 'union_type': 1408, 'intersection_type': 87, 'class': 405, 'function': 234, 'enum': 312, 'reexport_all': 6, 'export_assignment': 31, 'component': 1}
2025-05-26 13:50:04,469 - ComprehensiveRealSystemTest - INFO - Step 3: Testing indexing capabilities...
2025-05-26 13:50:04,469 - ComprehensiveRealSystemTest - INFO - === Testing Indexing Capabilities ===
2025-05-26 13:50:05,827 - ComprehensiveRealSystemTest - INFO - Qdrant collection created successfully
2025-05-26 13:50:05,827 - ComprehensiveRealSystemTest - INFO - Indexing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:50:12,776 - ComprehensiveRealSystemTest - INFO - Indexed 196 symbols in 6.948s
2025-05-26 13:50:12,776 - ComprehensiveRealSystemTest - INFO - Indexing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:50:20,030 - ComprehensiveRealSystemTest - INFO - Indexed 210 symbols in 7.254s
2025-05-26 13:50:20,031 - ComprehensiveRealSystemTest - INFO - Indexing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:51:13,272 - ComprehensiveRealSystemTest - INFO - Indexed 2293 symbols in 90.451s
2025-05-26 13:51:13,272 - ComprehensiveRealSystemTest - INFO - Indexing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:51:20,273 - ComprehensiveRealSystemTest - INFO - Indexed 147 symbols in 7.001s
2025-05-26 13:51:20,273 - ComprehensiveRealSystemTest - INFO - Indexing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:51:51,331 - ComprehensiveRealSystemTest - INFO - Indexed 2293 symbols in 91.299s
2025-05-26 13:51:51,331 - ComprehensiveRealSystemTest - INFO - Indexing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:51:59,082 - ComprehensiveRealSystemTest - INFO - Indexed 147 symbols in 7.751s
2025-05-26 13:51:59,082 - ComprehensiveRealSystemTest - INFO - Indexing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:52:34,996 - ComprehensiveRealSystemTest - INFO - Indexed 2009 symbols in 74.723s
2025-05-26 13:52:35,005 - ComprehensiveRealSystemTest - INFO - Indexing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:53:46,933 - ComprehensiveRealSystemTest - INFO - Indexed 2008 symbols in 71.928s
2025-05-26 13:53:46,933 - ComprehensiveRealSystemTest - INFO - Indexing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:54:51,596 - ComprehensiveRealSystemTest - INFO - Indexed 1940 symbols in 64.663s
2025-05-26 13:54:51,596 - ComprehensiveRealSystemTest - INFO - Indexing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 13:54:57,774 - ComprehensiveRealSystemTest - INFO - Indexed 243 symbols in 6.178s
2025-05-26 13:54:57,775 - ComprehensiveRealSystemTest - INFO - Indexing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 13:55:03,334 - ComprehensiveRealSystemTest - INFO - Indexed 244 symbols in 5.559s
2025-05-26 13:55:03,334 - ComprehensiveRealSystemTest - INFO - Indexing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:56:00,691 - ComprehensiveRealSystemTest - INFO - Indexed 1663 symbols in 57.357s
2025-05-26 13:56:00,693 - ComprehensiveRealSystemTest - INFO - Indexing file 11/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:56:56,524 - ComprehensiveRealSystemTest - INFO - Indexed 1662 symbols in 55.831s
2025-05-26 13:56:56,524 - ComprehensiveRealSystemTest - INFO - Indexing file 12/30: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-26 13:56:57,602 - ComprehensiveRealSystemTest - INFO - Indexed 40 symbols in 1.077s
2025-05-26 13:56:57,602 - ComprehensiveRealSystemTest - INFO - Indexing file 13/30: comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-26 13:56:58,636 - ComprehensiveRealSystemTest - INFO - Indexed 40 symbols in 1.034s
2025-05-26 13:56:58,636 - ComprehensiveRealSystemTest - INFO - Indexing file 14/30: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-26 13:56:59,774 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.138s
2025-05-26 13:56:59,774 - ComprehensiveRealSystemTest - INFO - Indexing file 15/30: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-26 13:57:01,272 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.498s
2025-05-26 13:57:01,272 - ComprehensiveRealSystemTest - INFO - Indexing file 16/30: Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-26 13:57:13,492 - ComprehensiveRealSystemTest - INFO - Indexed 385 symbols in 12.219s
2025-05-26 13:57:13,492 - ComprehensiveRealSystemTest - INFO - Indexing file 17/30: comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-26 13:57:22,727 - ComprehensiveRealSystemTest - INFO - Indexed 385 symbols in 9.235s
2025-05-26 13:57:22,727 - ComprehensiveRealSystemTest - INFO - Indexing file 18/30: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-26 13:57:24,652 - ComprehensiveRealSystemTest - INFO - Indexed 67 symbols in 1.924s
2025-05-26 13:57:24,652 - ComprehensiveRealSystemTest - INFO - Indexing file 19/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-26 13:57:42,824 - ComprehensiveRealSystemTest - INFO - Indexed 570 symbols in 18.173s
2025-05-26 13:57:42,824 - ComprehensiveRealSystemTest - INFO - Indexing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-26 13:57:43,992 - ComprehensiveRealSystemTest - INFO - Indexed 33 symbols in 1.168s
2025-05-26 13:57:43,992 - ComprehensiveRealSystemTest - INFO - Indexing file 21/30: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-26 13:57:45,091 - ComprehensiveRealSystemTest - INFO - Indexed 33 symbols in 1.099s
2025-05-26 13:57:45,105 - ComprehensiveRealSystemTest - INFO - Indexing file 22/30: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-26 13:57:45,931 - ComprehensiveRealSystemTest - INFO - Indexed 24 symbols in 0.826s
2025-05-26 13:57:45,931 - ComprehensiveRealSystemTest - INFO - Indexing file 23/30: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-26 13:57:46,619 - ComprehensiveRealSystemTest - INFO - Indexed 24 symbols in 0.689s
2025-05-26 13:57:46,619 - ComprehensiveRealSystemTest - INFO - Indexing file 24/30: Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-26 13:57:47,861 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.242s
2025-05-26 13:57:47,861 - ComprehensiveRealSystemTest - INFO - Indexing file 25/30: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-26 13:57:49,023 - ComprehensiveRealSystemTest - INFO - Indexed 45 symbols in 1.162s
2025-05-26 13:57:49,023 - ComprehensiveRealSystemTest - INFO - Indexing file 26/30: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-26 13:57:50,199 - ComprehensiveRealSystemTest - INFO - Indexed 45 symbols in 1.176s
2025-05-26 13:57:50,199 - ComprehensiveRealSystemTest - INFO - Indexing file 27/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-26 13:57:52,976 - ComprehensiveRealSystemTest - INFO - Indexed 105 symbols in 2.778s
2025-05-26 13:57:52,976 - ComprehensiveRealSystemTest - INFO - Indexing file 28/30: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-26 13:57:54,096 - ComprehensiveRealSystemTest - INFO - Indexed 38 symbols in 1.120s
2025-05-26 13:57:54,096 - ComprehensiveRealSystemTest - INFO - Indexing file 29/30: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-26 13:57:55,148 - ComprehensiveRealSystemTest - INFO - Indexed 38 symbols in 1.052s
2025-05-26 13:57:55,148 - ComprehensiveRealSystemTest - INFO - Indexing file 30/30: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-26 13:57:56,746 - ComprehensiveRealSystemTest - INFO - Indexed 60 symbols in 1.598s
2025-05-26 13:57:56,747 - ComprehensiveRealSystemTest - INFO - Indexing completed: 30/30 files successful
2025-05-26 13:57:56,747 - ComprehensiveRealSystemTest - INFO - Total symbols indexed: 14688
2025-05-26 13:57:56,748 - ComprehensiveRealSystemTest - INFO - Step 4: Testing agent capabilities...
2025-05-26 13:57:56,748 - ComprehensiveRealSystemTest - INFO - === Testing Agent Capabilities ===
2025-05-26 13:57:56,760 - ComprehensiveRealSystemTest - INFO - Testing query: 'Button' (type: component)
2025-05-26 13:57:56,760 - ComprehensiveRealSystemTest - ERROR - Error testing query 'Button': 'Function' object is not callable
2025-05-26 13:57:56,760 - ComprehensiveRealSystemTest - INFO - Testing query: 'Dialog' (type: component)
2025-05-26 13:57:56,760 - ComprehensiveRealSystemTest - ERROR - Error testing query 'Dialog': 'Function' object is not callable
2025-05-26 13:57:56,760 - ComprehensiveRealSystemTest - INFO - Testing query: 'Text' (type: component)
2025-05-26 13:57:56,760 - ComprehensiveRealSystemTest - ERROR - Error testing query 'Text': 'Function' object is not callable
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - INFO - Testing query: 'Image' (type: component)
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - ERROR - Error testing query 'Image': 'Function' object is not callable
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - INFO - Testing query: 'List' (type: component)
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - ERROR - Error testing query 'List': 'Function' object is not callable
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - INFO - Testing query: 'UIAbility' (type: api)
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - ERROR - Error testing query 'UIAbility': 'Function' object is not callable
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - INFO - Testing query: 'router' (type: api)
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - ERROR - Error testing query 'router': 'Function' object is not callable
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - INFO - Testing query: 'preferences' (type: api)
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - ERROR - Error testing query 'preferences': 'Function' object is not callable
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - INFO - Testing query: 'http' (type: api)
2025-05-26 13:57:56,761 - ComprehensiveRealSystemTest - ERROR - Error testing query 'http': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: 'bluetooth' (type: api)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query 'bluetooth': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.app.ability.UIAbility' (type: import_path)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.app.ability.UIAbility': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.router' (type: import_path)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.router': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.data.preferences' (type: import_path)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.data.preferences': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.net.http' (type: import_path)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.net.http': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.bluetooth' (type: import_path)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.bluetooth': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: 'file system' (type: api)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query 'file system': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: 'data storage' (type: api)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query 'data storage': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: 'network request' (type: api)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query 'network request': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: 'device sensor' (type: api)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query 'device sensor': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Testing query: 'multimedia audio' (type: api)
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - ERROR - Error testing query 'multimedia audio': 'Function' object is not callable
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Agent testing completed: 0/20 queries successful
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - === Generating Comprehensive Report ===
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Comprehensive report saved to test_results/Test_ComprehensiveRealSystem_Results/
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Test completed successfully!
2025-05-26 13:57:56,762 - ComprehensiveRealSystemTest - INFO - Comprehensive test completed in 512.17 seconds
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:0.6b
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/api
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.appAccount.d.ts (136,704 bytes)
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.account.osAccount.d.ts (59,446 bytes)
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.arkui.UIContext.d.ts (123,406 bytes)
2025-05-26 13:58:38,496 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:58:38,509 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:58:38,510 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:58:38,510 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.buffer.d.ts (190,085 bytes)
2025-05-26 13:58:38,510 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:58:38,510 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEvent.d.ts (65,130 bytes)
2025-05-26 13:58:38,510 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.commonEventManager.d.ts (68,365 bytes)
2025-05-26 13:58:38,511 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.contact.d.ts (143,754 bytes)
2025-05-26 13:58:38,511 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:58:38,511 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:58:38,511 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:58:38,512 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:58:38,512 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.display.d.ts (50,958 bytes)
2025-05-26 13:58:38,513 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:58:38,513 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:58:38,514 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:58:38,514 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.geoLocationManager.d.ts (95,526 bytes)
2025-05-26 13:58:38,515 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts (149,907 bytes)
2025-05-26 13:58:38,515 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts (50,405 bytes)
2025-05-26 13:58:38,515 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.i18n.d.ts (111,611 bytes)
2025-05-26 13:58:38,516 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethod.d.ts (75,816 bytes)
2025-05-26 13:58:38,516 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.inputMethodEngine.d.ts (82,240 bytes)
2025-05-26 13:58:38,516 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.intl.d.ts (116,417 bytes)
2025-05-26 13:58:38,516 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:58:38,517 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:58:38,517 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:58:38,517 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:58:38,517 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:58:38,517 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:58:38,518 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts (51,152 bytes)
2025-05-26 13:58:38,518 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:58:38,519 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:58:38,519 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:58:38,519 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.nfc.tag.d.ts (60,146 bytes)
2025-05-26 13:58:38,520 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.notificationManager.d.ts (67,491 bytes)
2025-05-26 13:58:38,520 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.pasteboard.d.ts (57,975 bytes)
2025-05-26 13:58:38,520 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:58:38,520 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.resourceManager.d.ts (245,843 bytes)
2025-05-26 13:58:38,521 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.router.d.ts (50,688 bytes)
2025-05-26 13:58:38,521 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.rpc.d.ts (170,228 bytes)
2025-05-26 13:58:38,521 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cert.d.ts (214,222 bytes)
2025-05-26 13:58:38,521 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts (336,615 bytes)
2025-05-26 13:58:38,521 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.security.huks.d.ts (186,266 bytes)
2025-05-26 13:58:38,522 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:58:38,522 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.UiTest.d.ts (158,743 bytes)
2025-05-26 13:58:38,522 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.userIAM.userAuth.d.ts (55,963 bytes)
2025-05-26 13:58:38,523 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.util.d.ts (198,770 bytes)
2025-05-26 13:58:38,523 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.netErrorList.d.ts (61,804 bytes)
2025-05-26 13:58:38,524 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts (267,704 bytes)
2025-05-26 13:58:38,524 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:58:38,524 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.window.d.ts (286,762 bytes)
2025-05-26 13:58:38,524 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.worker.d.ts (86,613 bytes)
2025-05-26 13:58:38,525 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\@ohos.zlib.d.ts (98,263 bytes)
2025-05-26 13:58:38,527 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\AbilityDelegator.d.ts (66,895 bytes)
2025-05-26 13:58:38,528 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\application\UIAbilityContext.d.ts (109,096 bytes)
2025-05-26 13:58:38,532 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/api\tag\nfctech.d.ts (69,043 bytes)
2025-05-26 13:58:38,533 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/component
2025-05-26 13:58:38,534 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\canvas.d.ts (209,139 bytes)
2025-05-26 13:58:38,535 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common.d.ts (566,343 bytes)
2025-05-26 13:58:38,535 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:58:38,535 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\enums.d.ts (205,914 bytes)
2025-05-26 13:58:38,536 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\gesture.d.ts (97,133 bytes)
2025-05-26 13:58:38,536 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\navigation.d.ts (83,995 bytes)
2025-05-26 13:58:38,537 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:58:38,538 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\swiper.d.ts (55,612 bytes)
2025-05-26 13:58:38,538 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\text_input.d.ts (62,924 bytes)
2025-05-26 13:58:38,538 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\units.d.ts (68,870 bytes)
2025-05-26 13:58:38,538 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/component\web.d.ts (231,955 bytes)
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/build-tools
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\aot\src\lib_ark_builtins.d.ts (54,703 bytes)
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts (842,681 bytes)
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.es5.d.ts (212,312 bytes)
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.webworker.d.ts (259,617 bytes)
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\protocol.d.ts (98,216 bytes)
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts (587,234 bytes)
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts (447,512 bytes)
2025-05-26 13:58:38,539 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts (447,499 bytes)
2025-05-26 13:58:38,552 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\canvas.d.ts (209,151 bytes)
2025-05-26 13:58:38,553 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts (566,649 bytes)
2025-05-26 13:58:38,553 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 13:58:38,553 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\enums.d.ts (205,914 bytes)
2025-05-26 13:58:38,554 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\gesture.d.ts (97,133 bytes)
2025-05-26 13:58:38,554 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\navigation.d.ts (84,079 bytes)
2025-05-26 13:58:38,556 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\rich_editor.d.ts (94,325 bytes)
2025-05-26 13:58:38,557 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\swiper.d.ts (55,612 bytes)
2025-05-26 13:58:38,557 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\text_input.d.ts (62,924 bytes)
2025-05-26 13:58:38,557 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\units.d.ts (68,936 bytes)
2025-05-26 13:58:38,558 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\web.d.ts (231,967 bytes)
2025-05-26 13:58:38,591 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index-legacy.d.ts (168,429 bytes)
2025-05-26 13:58:38,591 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts (611,184 bytes)
2025-05-26 13:58:38,608 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts (52,155 bytes)
2025-05-26 13:58:38,608 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts (52,150 bytes)
2025-05-26 13:58:38,610 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\buffer.d.ts (107,224 bytes)
2025-05-26 13:58:38,610 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\child_process.d.ts (67,998 bytes)
2025-05-26 13:58:38,611 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\crypto.d.ts (183,978 bytes)
2025-05-26 13:58:38,611 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs.d.ts (182,973 bytes)
2025-05-26 13:58:38,611 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http.d.ts (79,056 bytes)
2025-05-26 13:58:38,611 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http2.d.ts (113,815 bytes)
2025-05-26 13:58:38,611 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\inspector.d.ts (125,369 bytes)
2025-05-26 13:58:38,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\process.d.ts (73,684 bytes)
2025-05-26 13:58:38,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\stream.d.ts (70,767 bytes)
2025-05-26 13:58:38,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\tls.d.ts (54,923 bytes)
2025-05-26 13:58:38,612 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\util.d.ts (82,275 bytes)
2025-05-26 13:58:38,613 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts (53,450 bytes)
2025-05-26 13:58:38,614 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\buffer.d.ts (107,224 bytes)
2025-05-26 13:58:38,614 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\child_process.d.ts (67,998 bytes)
2025-05-26 13:58:38,614 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\crypto.d.ts (183,913 bytes)
2025-05-26 13:58:38,615 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs.d.ts (182,973 bytes)
2025-05-26 13:58:38,615 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http.d.ts (79,056 bytes)
2025-05-26 13:58:38,615 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http2.d.ts (113,815 bytes)
2025-05-26 13:58:38,615 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\inspector.d.ts (125,369 bytes)
2025-05-26 13:58:38,615 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\process.d.ts (73,684 bytes)
2025-05-26 13:58:38,616 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\stream.d.ts (72,452 bytes)
2025-05-26 13:58:38,616 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\tls.d.ts (54,923 bytes)
2025-05-26 13:58:38,616 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\util.d.ts (82,262 bytes)
2025-05-26 13:58:38,618 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts (53,380 bytes)
2025-05-26 13:58:38,623 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\dist\generated\ast-spec.d.ts (68,378 bytes)
2025-05-26 13:58:38,623 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\_ts3.4\dist\generated\ast-spec.d.ts (70,137 bytes)
2025-05-26 13:58:38,761 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts (816,786 bytes)
2025-05-26 13:58:38,777 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.es5.d.ts (213,196 bytes)
2025-05-26 13:58:38,777 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts (272,256 bytes)
2025-05-26 13:58:38,777 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\protocol.d.ts (107,934 bytes)
2025-05-26 13:58:38,777 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts (747,710 bytes)
2025-05-26 13:58:38,777 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts (598,789 bytes)
2025-05-26 13:58:38,777 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts (598,776 bytes)
2025-05-26 13:58:38,777 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts (335,559 bytes)
2025-05-26 13:58:38,798 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/api
2025-05-26 13:58:38,801 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:58:38,801 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:58:38,802 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:58:38,802 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:58:38,802 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:58:38,803 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:58:38,803 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:58:38,803 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:58:38,803 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:58:38,804 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:58:38,804 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:58:38,804 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:58:38,804 - ComprehensiveRealSystemTest - INFO - Found large file: Information/default/hms/ets/api\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:58:38,805 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/kits
2025-05-26 13:58:38,807 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/hms/ets/component
2025-05-26 13:58:38,807 - ComprehensiveRealSystemTest - INFO - Searching in: comprehensive_dataset
2025-05-26 13:58:38,808 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\basic_ui\text_input.d.ts (62,924 bytes)
2025-05-26 13:58:38,809 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\container\swiper.d.ts (55,612 bytes)
2025-05-26 13:58:38,810 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 13:58:38,810 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 13:58:38,810 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 13:58:38,810 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 13:58:38,811 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\device\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 13:58:38,811 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\form\text_input.d.ts (62,924 bytes)
2025-05-26 13:58:38,815 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 13:58:38,815 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 13:58:38,816 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 13:58:38,816 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 13:58:38,816 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 13:58:38,816 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 13:58:38,817 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 13:58:38,817 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 13:58:38,817 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 13:58:38,818 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 13:58:38,818 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 13:58:38,818 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 13:58:38,818 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\hms\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 13:58:38,820 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 13:58:38,820 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 13:58:38,820 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 13:58:38,820 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 13:58:38,821 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 13:58:38,821 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 13:58:38,821 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\navigation\navigation.d.ts (83,995 bytes)
2025-05-26 13:58:38,821 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 13:58:38,822 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 13:58:38,822 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 13:58:38,822 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\network\@ohos.request.d.ts (192,677 bytes)
2025-05-26 13:58:38,823 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 13:58:38,823 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 13:58:38,823 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 13:58:38,823 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 13:58:38,824 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 13:58:38,824 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 13:58:38,824 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\storage\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 13:58:38,824 - ComprehensiveRealSystemTest - INFO - Found large file: comprehensive_dataset\system\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 13:58:38,824 - ComprehensiveRealSystemTest - INFO - Found 30 large ArkTS files for testing
2025-05-26 13:58:38,824 - ComprehensiveRealSystemTest - INFO - Found 30 large files for testing
2025-05-26 13:58:38,824 - ComprehensiveRealSystemTest - INFO - Step 2: Testing parsing capabilities...
2025-05-26 13:58:38,828 - ComprehensiveRealSystemTest - INFO - === Testing Parsing Capabilities ===
2025-05-26 13:58:38,830 - ComprehensiveRealSystemTest - INFO - Parsing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:58:38,901 - ComprehensiveRealSystemTest - INFO - Parsed 196 symbols in 0.070s
2025-05-26 13:58:38,902 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'type': 1, 'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 1, 'union_type': 176, 'intersection_type': 1}
2025-05-26 13:58:38,902 - ComprehensiveRealSystemTest - INFO - Parsing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:58:38,956 - ComprehensiveRealSystemTest - INFO - Parsed 210 symbols in 0.054s
2025-05-26 13:58:38,956 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 2, 'union_type': 190, 'intersection_type': 1}
2025-05-26 13:58:38,956 - ComprehensiveRealSystemTest - INFO - Parsing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:58:39,146 - ComprehensiveRealSystemTest - INFO - Parsed 2293 symbols in 0.190s
2025-05-26 13:58:39,146 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 45, 'interface': 1244, 'type': 185, 'function': 50, 'namespace': 110, 'enum': 41, 'const': 445, 'reexport_all': 1, 'callback_type': 12, 'union_type': 145, 'intersection_type': 12, 'export_assignment': 3}
2025-05-26 13:58:39,146 - ComprehensiveRealSystemTest - INFO - Parsing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 13:58:39,202 - ComprehensiveRealSystemTest - INFO - Parsed 147 symbols in 0.056s
2025-05-26 13:58:39,202 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'const': 88, 'union_type': 57}
2025-05-26 13:58:39,202 - ComprehensiveRealSystemTest - INFO - Parsing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:58:39,352 - ComprehensiveRealSystemTest - INFO - Parsed 2009 symbols in 0.150s
2025-05-26 13:58:39,352 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 3}
2025-05-26 13:58:39,352 - ComprehensiveRealSystemTest - INFO - Parsing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:58:39,502 - ComprehensiveRealSystemTest - INFO - Parsed 2008 symbols in 0.150s
2025-05-26 13:58:39,502 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 2}
2025-05-26 13:58:39,502 - ComprehensiveRealSystemTest - INFO - Parsing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 13:58:39,619 - ComprehensiveRealSystemTest - INFO - Parsed 1940 symbols in 0.117s
2025-05-26 13:58:39,619 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 27, 'interface': 1106, 'type': 169, 'function': 36, 'namespace': 41, 'enum': 39, 'const': 363, 'reexport_all': 1, 'callback_type': 12, 'union_type': 133, 'intersection_type': 11, 'export_assignment': 2}
2025-05-26 13:58:39,619 - ComprehensiveRealSystemTest - INFO - Parsing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 13:58:39,702 - ComprehensiveRealSystemTest - INFO - Parsed 243 symbols in 0.083s
2025-05-26 13:58:39,702 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2}
2025-05-26 13:58:39,702 - ComprehensiveRealSystemTest - INFO - Parsing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 13:58:39,786 - ComprehensiveRealSystemTest - INFO - Parsed 244 symbols in 0.084s
2025-05-26 13:58:39,786 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2, 'component': 1}
2025-05-26 13:58:39,786 - ComprehensiveRealSystemTest - INFO - Parsing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 13:58:39,888 - ComprehensiveRealSystemTest - INFO - Parsed 1663 symbols in 0.102s
2025-05-26 13:58:39,888 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 2}
2025-05-26 13:58:39,888 - ComprehensiveRealSystemTest - INFO - Parsing file 11/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 13:58:39,987 - ComprehensiveRealSystemTest - INFO - Parsed 1662 symbols in 0.099s
2025-05-26 13:58:39,987 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 1}
2025-05-26 13:58:39,987 - ComprehensiveRealSystemTest - INFO - Parsing file 12/30: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-26 13:58:40,020 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.033s
2025-05-26 13:58:40,022 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:58:40,022 - ComprehensiveRealSystemTest - INFO - Parsing file 13/30: comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-26 13:58:40,054 - ComprehensiveRealSystemTest - INFO - Parsed 40 symbols in 0.032s
2025-05-26 13:58:40,054 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'const': 10, 'callback_type': 1, 'export_assignment': 1}
2025-05-26 13:58:40,054 - ComprehensiveRealSystemTest - INFO - Parsing file 14/30: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-26 13:58:40,117 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.061s
2025-05-26 13:58:40,117 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:58:40,117 - ComprehensiveRealSystemTest - INFO - Parsing file 15/30: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-26 13:58:40,170 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.054s
2025-05-26 13:58:40,170 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'union_type': 2, 'export_assignment': 1}
2025-05-26 13:58:40,170 - ComprehensiveRealSystemTest - INFO - Parsing file 16/30: Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-26 13:58:40,254 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.083s
2025-05-26 13:58:40,254 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:58:40,254 - ComprehensiveRealSystemTest - INFO - Parsing file 17/30: comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-26 13:58:40,337 - ComprehensiveRealSystemTest - INFO - Parsed 385 symbols in 0.083s
2025-05-26 13:58:40,337 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'const': 258, 'union_type': 10, 'export_assignment': 1}
2025-05-26 13:58:40,337 - ComprehensiveRealSystemTest - INFO - Parsing file 18/30: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-26 13:58:40,392 - ComprehensiveRealSystemTest - INFO - Parsed 67 symbols in 0.055s
2025-05-26 13:58:40,392 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 6, 'interface': 59, 'export_assignment': 1}
2025-05-26 13:58:40,392 - ComprehensiveRealSystemTest - INFO - Parsing file 19/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-26 13:58:40,454 - ComprehensiveRealSystemTest - INFO - Parsed 570 symbols in 0.063s
2025-05-26 13:58:40,455 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 122, 'interface': 263, 'type': 1, 'namespace': 65, 'const': 26, 'callback_type': 1, 'union_type': 55, 'intersection_type': 36, 'export_assignment': 1}
2025-05-26 13:58:40,455 - ComprehensiveRealSystemTest - INFO - Parsing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-26 13:58:40,504 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.049s
2025-05-26 13:58:40,504 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:58:40,504 - ComprehensiveRealSystemTest - INFO - Parsing file 21/30: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-26 13:58:40,554 - ComprehensiveRealSystemTest - INFO - Parsed 33 symbols in 0.048s
2025-05-26 13:58:40,554 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'const': 2, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:58:40,554 - ComprehensiveRealSystemTest - INFO - Parsing file 22/30: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-26 13:58:40,607 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.053s
2025-05-26 13:58:40,607 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:58:40,607 - ComprehensiveRealSystemTest - INFO - Parsing file 23/30: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-26 13:58:40,654 - ComprehensiveRealSystemTest - INFO - Parsed 24 symbols in 0.047s
2025-05-26 13:58:40,654 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:58:40,654 - ComprehensiveRealSystemTest - INFO - Parsing file 24/30: Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-26 13:58:40,687 - ComprehensiveRealSystemTest - INFO - Parsed 47 symbols in 0.034s
2025-05-26 13:58:40,687 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 44, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:58:40,687 - ComprehensiveRealSystemTest - INFO - Parsing file 25/30: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-26 13:58:40,740 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.052s
2025-05-26 13:58:40,741 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:58:40,741 - ComprehensiveRealSystemTest - INFO - Parsing file 26/30: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-26 13:58:40,783 - ComprehensiveRealSystemTest - INFO - Parsed 45 symbols in 0.043s
2025-05-26 13:58:40,783 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-26 13:58:40,783 - ComprehensiveRealSystemTest - INFO - Parsing file 27/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-26 13:58:40,806 - ComprehensiveRealSystemTest - INFO - Parsed 105 symbols in 0.023s
2025-05-26 13:58:40,806 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 14, 'union_type': 90}
2025-05-26 13:58:40,806 - ComprehensiveRealSystemTest - INFO - Parsing file 28/30: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-26 13:58:40,839 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.033s
2025-05-26 13:58:40,839 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:58:40,839 - ComprehensiveRealSystemTest - INFO - Parsing file 29/30: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-26 13:58:40,889 - ComprehensiveRealSystemTest - INFO - Parsed 38 symbols in 0.050s
2025-05-26 13:58:40,890 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-26 13:58:40,890 - ComprehensiveRealSystemTest - INFO - Parsing file 30/30: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-26 13:58:40,931 - ComprehensiveRealSystemTest - INFO - Parsed 60 symbols in 0.041s
2025-05-26 13:58:40,932 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 25, 'interface': 31, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 13:58:40,933 - ComprehensiveRealSystemTest - INFO - Parsing completed: 30/30 files successful
2025-05-26 13:58:40,934 - ComprehensiveRealSystemTest - INFO - Total symbols parsed: 14688
2025-05-26 13:58:40,934 - ComprehensiveRealSystemTest - INFO - Symbol types found: {'type': 1132, 'namespace': 604, 'interface': 7257, 'const': 3102, 'callback_type': 109, 'union_type': 1408, 'intersection_type': 87, 'class': 405, 'function': 234, 'enum': 312, 'reexport_all': 6, 'export_assignment': 31, 'component': 1}
2025-05-26 13:58:40,934 - ComprehensiveRealSystemTest - INFO - Step 3: Testing indexing capabilities...
2025-05-26 13:58:40,934 - ComprehensiveRealSystemTest - INFO - === Testing Indexing Capabilities ===
2025-05-26 13:58:42,286 - ComprehensiveRealSystemTest - INFO - Qdrant collection created successfully
2025-05-26 13:58:42,286 - ComprehensiveRealSystemTest - INFO - Indexing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:58:47,072 - ComprehensiveRealSystemTest - INFO - Indexed 196 symbols in 4.786s
2025-05-26 13:58:47,072 - ComprehensiveRealSystemTest - INFO - Indexing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 13:58:52,072 - ComprehensiveRealSystemTest - INFO - Indexed 210 symbols in 4.999s
2025-05-26 13:58:52,072 - ComprehensiveRealSystemTest - INFO - Indexing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 14:00:11,586 - ComprehensiveRealSystemTest - INFO - Indexed 2293 symbols in 79.514s
2025-05-26 14:00:11,587 - ComprehensiveRealSystemTest - INFO - Indexing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 14:00:17,998 - ComprehensiveRealSystemTest - INFO - Indexed 147 symbols in 6.411s
2025-05-26 14:00:17,998 - ComprehensiveRealSystemTest - INFO - Indexing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 14:01:32,410 - ComprehensiveRealSystemTest - INFO - Indexed 2009 symbols in 74.411s
2025-05-26 14:01:32,410 - ComprehensiveRealSystemTest - INFO - Indexing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 14:02:41,641 - ComprehensiveRealSystemTest - INFO - Indexed 2008 symbols in 69.231s
2025-05-26 14:02:41,642 - ComprehensiveRealSystemTest - INFO - Indexing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 14:03:47,216 - ComprehensiveRealSystemTest - INFO - Indexed 1940 symbols in 65.575s
2025-05-26 14:03:47,216 - ComprehensiveRealSystemTest - INFO - Indexing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 14:03:53,926 - ComprehensiveRealSystemTest - INFO - Indexed 243 symbols in 6.709s
2025-05-26 14:03:53,926 - ComprehensiveRealSystemTest - INFO - Indexing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 14:04:01,003 - ComprehensiveRealSystemTest - INFO - Indexed 244 symbols in 7.077s
2025-05-26 14:04:01,003 - ComprehensiveRealSystemTest - INFO - Indexing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 14:04:58,492 - ComprehensiveRealSystemTest - INFO - Indexed 1663 symbols in 57.489s
2025-05-26 14:04:58,492 - ComprehensiveRealSystemTest - INFO - Indexing file 11/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 14:05:55,375 - ComprehensiveRealSystemTest - INFO - Indexed 1662 symbols in 56.882s
2025-05-26 14:05:55,375 - ComprehensiveRealSystemTest - INFO - Indexing file 12/30: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-26 14:05:56,416 - ComprehensiveRealSystemTest - INFO - Indexed 40 symbols in 1.040s
2025-05-26 14:05:56,416 - ComprehensiveRealSystemTest - INFO - Indexing file 13/30: comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-26 14:05:57,385 - ComprehensiveRealSystemTest - INFO - Indexed 40 symbols in 0.969s
2025-05-26 14:05:57,385 - ComprehensiveRealSystemTest - INFO - Indexing file 14/30: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-26 14:05:58,477 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.092s
2025-05-26 14:05:58,477 - ComprehensiveRealSystemTest - INFO - Indexing file 15/30: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-26 14:05:59,574 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.097s
2025-05-26 14:05:59,574 - ComprehensiveRealSystemTest - INFO - Indexing file 16/30: Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-26 14:06:08,598 - ComprehensiveRealSystemTest - INFO - Indexed 385 symbols in 9.024s
2025-05-26 14:06:08,598 - ComprehensiveRealSystemTest - INFO - Indexing file 17/30: comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-26 14:06:17,787 - ComprehensiveRealSystemTest - INFO - Indexed 385 symbols in 9.188s
2025-05-26 14:06:17,787 - ComprehensiveRealSystemTest - INFO - Indexing file 18/30: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-26 14:06:19,437 - ComprehensiveRealSystemTest - INFO - Indexed 67 symbols in 1.650s
2025-05-26 14:06:19,437 - ComprehensiveRealSystemTest - INFO - Indexing file 19/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-26 14:06:37,046 - ComprehensiveRealSystemTest - INFO - Indexed 570 symbols in 17.609s
2025-05-26 14:06:37,046 - ComprehensiveRealSystemTest - INFO - Indexing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-26 14:06:38,125 - ComprehensiveRealSystemTest - INFO - Indexed 33 symbols in 1.079s
2025-05-26 14:06:38,125 - ComprehensiveRealSystemTest - INFO - Indexing file 21/30: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-26 14:06:39,017 - ComprehensiveRealSystemTest - INFO - Indexed 33 symbols in 0.891s
2025-05-26 14:06:39,017 - ComprehensiveRealSystemTest - INFO - Indexing file 22/30: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-26 14:06:39,627 - ComprehensiveRealSystemTest - INFO - Indexed 24 symbols in 0.611s
2025-05-26 14:06:39,627 - ComprehensiveRealSystemTest - INFO - Indexing file 23/30: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-26 14:06:40,390 - ComprehensiveRealSystemTest - INFO - Indexed 24 symbols in 0.763s
2025-05-26 14:06:40,390 - ComprehensiveRealSystemTest - INFO - Indexing file 24/30: Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-26 14:06:41,891 - ComprehensiveRealSystemTest - INFO - Indexed 47 symbols in 1.501s
2025-05-26 14:06:41,891 - ComprehensiveRealSystemTest - INFO - Indexing file 25/30: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-26 14:06:43,384 - ComprehensiveRealSystemTest - INFO - Indexed 45 symbols in 1.493s
2025-05-26 14:06:43,384 - ComprehensiveRealSystemTest - INFO - Indexing file 26/30: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-26 14:06:44,847 - ComprehensiveRealSystemTest - INFO - Indexed 45 symbols in 1.463s
2025-05-26 14:06:44,847 - ComprehensiveRealSystemTest - INFO - Indexing file 27/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-26 14:06:48,252 - ComprehensiveRealSystemTest - INFO - Indexed 105 symbols in 3.405s
2025-05-26 14:06:48,252 - ComprehensiveRealSystemTest - INFO - Indexing file 28/30: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-26 14:06:49,501 - ComprehensiveRealSystemTest - INFO - Indexed 38 symbols in 1.249s
2025-05-26 14:06:49,501 - ComprehensiveRealSystemTest - INFO - Indexing file 29/30: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-26 14:06:50,793 - ComprehensiveRealSystemTest - INFO - Indexed 38 symbols in 1.292s
2025-05-26 14:06:50,793 - ComprehensiveRealSystemTest - INFO - Indexing file 30/30: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-26 14:06:52,762 - ComprehensiveRealSystemTest - INFO - Indexed 60 symbols in 1.968s
2025-05-26 14:06:52,763 - ComprehensiveRealSystemTest - INFO - Indexing completed: 30/30 files successful
2025-05-26 14:06:52,763 - ComprehensiveRealSystemTest - INFO - Total symbols indexed: 14688
2025-05-26 14:06:52,763 - ComprehensiveRealSystemTest - INFO - Step 4: Testing agent capabilities...
2025-05-26 14:06:52,764 - ComprehensiveRealSystemTest - INFO - === Testing Agent Capabilities ===
2025-05-26 14:06:52,788 - ComprehensiveRealSystemTest - INFO - Testing query: 'Button' (type: component)
2025-05-26 14:06:52,843 - ComprehensiveRealSystemTest - INFO - Query successful in 0.055s, results length: 17
2025-05-26 14:06:52,843 - ComprehensiveRealSystemTest - INFO - Testing query: 'Dialog' (type: component)
2025-05-26 14:06:52,871 - ComprehensiveRealSystemTest - INFO - Query successful in 0.028s, results length: 17
2025-05-26 14:06:52,872 - ComprehensiveRealSystemTest - INFO - Testing query: 'Text' (type: component)
2025-05-26 14:06:52,901 - ComprehensiveRealSystemTest - INFO - Query successful in 0.028s, results length: 17
2025-05-26 14:06:52,902 - ComprehensiveRealSystemTest - INFO - Testing query: 'Image' (type: component)
2025-05-26 14:06:52,932 - ComprehensiveRealSystemTest - INFO - Query successful in 0.030s, results length: 17
2025-05-26 14:06:52,932 - ComprehensiveRealSystemTest - INFO - Testing query: 'List' (type: component)
2025-05-26 14:06:52,964 - ComprehensiveRealSystemTest - INFO - Query successful in 0.033s, results length: 17
2025-05-26 14:06:52,964 - ComprehensiveRealSystemTest - INFO - Testing query: 'UIAbility' (type: api)
2025-05-26 14:06:53,002 - ComprehensiveRealSystemTest - INFO - Query successful in 0.037s, results length: 17
2025-05-26 14:06:53,002 - ComprehensiveRealSystemTest - INFO - Testing query: 'router' (type: api)
2025-05-26 14:06:53,090 - ComprehensiveRealSystemTest - INFO - Query successful in 0.087s, results length: 742
2025-05-26 14:06:53,091 - ComprehensiveRealSystemTest - INFO - Testing query: 'preferences' (type: api)
2025-05-26 14:06:53,124 - ComprehensiveRealSystemTest - INFO - Query successful in 0.033s, results length: 1962
2025-05-26 14:06:53,124 - ComprehensiveRealSystemTest - INFO - Testing query: 'http' (type: api)
2025-05-26 14:06:53,214 - ComprehensiveRealSystemTest - INFO - Query successful in 0.089s, results length: 1587
2025-05-26 14:06:53,214 - ComprehensiveRealSystemTest - INFO - Testing query: 'bluetooth' (type: api)
2025-05-26 14:06:53,249 - ComprehensiveRealSystemTest - INFO - Query successful in 0.034s, results length: 17
2025-05-26 14:06:53,249 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.app.ability.UIAbility' (type: import_path)
2025-05-26 14:06:53,291 - ComprehensiveRealSystemTest - INFO - Query successful in 0.043s, results length: 17
2025-05-26 14:06:53,291 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.router' (type: import_path)
2025-05-26 14:06:53,323 - ComprehensiveRealSystemTest - INFO - Query successful in 0.031s, results length: 17
2025-05-26 14:06:53,323 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.data.preferences' (type: import_path)
2025-05-26 14:06:53,353 - ComprehensiveRealSystemTest - INFO - Query successful in 0.031s, results length: 17
2025-05-26 14:06:53,353 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.net.http' (type: import_path)
2025-05-26 14:06:53,384 - ComprehensiveRealSystemTest - INFO - Query successful in 0.031s, results length: 17
2025-05-26 14:06:53,384 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.bluetooth' (type: import_path)
2025-05-26 14:06:53,418 - ComprehensiveRealSystemTest - INFO - Query successful in 0.032s, results length: 17
2025-05-26 14:06:53,418 - ComprehensiveRealSystemTest - INFO - Testing query: 'file system' (type: api)
2025-05-26 14:06:53,494 - ComprehensiveRealSystemTest - INFO - Query successful in 0.076s, results length: 1321
2025-05-26 14:06:53,495 - ComprehensiveRealSystemTest - INFO - Testing query: 'data storage' (type: api)
2025-05-26 14:06:53,529 - ComprehensiveRealSystemTest - INFO - Query successful in 0.034s, results length: 17
2025-05-26 14:06:53,529 - ComprehensiveRealSystemTest - INFO - Testing query: 'network request' (type: api)
2025-05-26 14:06:53,577 - ComprehensiveRealSystemTest - INFO - Query successful in 0.048s, results length: 17
2025-05-26 14:06:53,577 - ComprehensiveRealSystemTest - INFO - Testing query: 'device sensor' (type: api)
2025-05-26 14:06:53,623 - ComprehensiveRealSystemTest - INFO - Query successful in 0.046s, results length: 17
2025-05-26 14:06:53,624 - ComprehensiveRealSystemTest - INFO - Testing query: 'multimedia audio' (type: api)
2025-05-26 14:06:53,670 - ComprehensiveRealSystemTest - INFO - Query successful in 0.046s, results length: 17
2025-05-26 14:06:53,671 - ComprehensiveRealSystemTest - INFO - Agent testing completed: 20/20 queries successful
2025-05-26 14:06:53,672 - ComprehensiveRealSystemTest - INFO - === Generating Comprehensive Report ===
2025-05-26 14:06:53,674 - ComprehensiveRealSystemTest - INFO - Comprehensive report saved to test_results/Test_ComprehensiveRealSystem_Results/
2025-05-26 14:06:53,675 - ComprehensiveRealSystemTest - INFO - Test completed successfully!
2025-05-26 14:06:53,675 - ComprehensiveRealSystemTest - INFO - Comprehensive test completed in 495.18 seconds
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:0.6b
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Searching for ArkTS files with different selection strategy (min size: 50,000 bytes)
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/api
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.account.appAccount.d.ts (136,704 bytes)
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.account.osAccount.d.ts (59,446 bytes)
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.arkui.UIContext.d.ts (123,406 bytes)
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 14:28:28,713 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 14:28:28,725 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 14:28:28,725 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.buffer.d.ts (190,085 bytes)
2025-05-26 14:28:28,725 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 14:28:28,727 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.commonEvent.d.ts (65,130 bytes)
2025-05-26 14:28:28,727 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.commonEventManager.d.ts (68,365 bytes)
2025-05-26 14:28:28,727 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.contact.d.ts (143,754 bytes)
2025-05-26 14:28:28,727 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 14:28:28,728 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 14:28:28,728 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 14:28:28,728 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 14:28:28,728 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.display.d.ts (50,958 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.geoLocationManager.d.ts (95,526 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts (149,907 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts (50,405 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.i18n.d.ts (111,611 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.inputMethod.d.ts (75,816 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.inputMethodEngine.d.ts (82,240 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.intl.d.ts (116,417 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts (51,152 bytes)
2025-05-26 14:28:28,730 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 14:28:28,735 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 14:28:28,735 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 14:28:28,735 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.nfc.tag.d.ts (60,146 bytes)
2025-05-26 14:28:28,735 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.notificationManager.d.ts (67,491 bytes)
2025-05-26 14:28:28,735 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.pasteboard.d.ts (57,975 bytes)
2025-05-26 14:28:28,735 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.request.d.ts (192,677 bytes)
2025-05-26 14:28:28,737 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.resourceManager.d.ts (245,843 bytes)
2025-05-26 14:28:28,737 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.router.d.ts (50,688 bytes)
2025-05-26 14:28:28,737 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.rpc.d.ts (170,228 bytes)
2025-05-26 14:28:28,737 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.security.cert.d.ts (214,222 bytes)
2025-05-26 14:28:28,737 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts (336,615 bytes)
2025-05-26 14:28:28,737 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.security.huks.d.ts (186,266 bytes)
2025-05-26 14:28:28,738 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 14:28:28,738 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.UiTest.d.ts (158,743 bytes)
2025-05-26 14:28:28,739 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.userIAM.userAuth.d.ts (55,963 bytes)
2025-05-26 14:28:28,739 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.util.d.ts (198,770 bytes)
2025-05-26 14:28:28,740 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.web.netErrorList.d.ts (61,804 bytes)
2025-05-26 14:28:28,740 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.web.webview.d.ts (267,704 bytes)
2025-05-26 14:28:28,740 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 14:28:28,740 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.window.d.ts (286,762 bytes)
2025-05-26 14:28:28,740 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.worker.d.ts (86,613 bytes)
2025-05-26 14:28:28,740 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\@ohos.zlib.d.ts (98,263 bytes)
2025-05-26 14:28:28,740 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\application\AbilityDelegator.d.ts (66,895 bytes)
2025-05-26 14:28:28,740 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\application\UIAbilityContext.d.ts (109,096 bytes)
2025-05-26 14:28:28,750 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/api\tag\nfctech.d.ts (69,043 bytes)
2025-05-26 14:28:28,751 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/component
2025-05-26 14:28:28,752 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\canvas.d.ts (209,139 bytes)
2025-05-26 14:28:28,752 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\common.d.ts (566,343 bytes)
2025-05-26 14:28:28,753 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 14:28:28,753 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\enums.d.ts (205,914 bytes)
2025-05-26 14:28:28,754 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\gesture.d.ts (97,133 bytes)
2025-05-26 14:28:28,754 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\navigation.d.ts (83,995 bytes)
2025-05-26 14:28:28,755 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\rich_editor.d.ts (94,325 bytes)
2025-05-26 14:28:28,756 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\swiper.d.ts (55,612 bytes)
2025-05-26 14:28:28,757 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\text_input.d.ts (62,924 bytes)
2025-05-26 14:28:28,757 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\units.d.ts (68,870 bytes)
2025-05-26 14:28:28,758 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/component\web.d.ts (231,955 bytes)
2025-05-26 14:28:28,758 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/build-tools
2025-05-26 14:28:28,759 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\aot\src\lib_ark_builtins.d.ts (54,703 bytes)
2025-05-26 14:28:28,760 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts (842,681 bytes)
2025-05-26 14:28:28,763 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.es5.d.ts (212,312 bytes)
2025-05-26 14:28:28,763 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.webworker.d.ts (259,617 bytes)
2025-05-26 14:28:28,764 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\protocol.d.ts (98,216 bytes)
2025-05-26 14:28:28,764 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts (587,234 bytes)
2025-05-26 14:28:28,764 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts (447,512 bytes)
2025-05-26 14:28:28,764 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts (447,499 bytes)
2025-05-26 14:28:28,772 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\canvas.d.ts (209,151 bytes)
2025-05-26 14:28:28,773 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts (566,649 bytes)
2025-05-26 14:28:28,773 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-26 14:28:28,773 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\enums.d.ts (205,914 bytes)
2025-05-26 14:28:28,774 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\gesture.d.ts (97,133 bytes)
2025-05-26 14:28:28,775 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\navigation.d.ts (84,079 bytes)
2025-05-26 14:28:28,776 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\rich_editor.d.ts (94,325 bytes)
2025-05-26 14:28:28,777 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\swiper.d.ts (55,612 bytes)
2025-05-26 14:28:28,778 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\text_input.d.ts (62,924 bytes)
2025-05-26 14:28:28,778 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\units.d.ts (68,936 bytes)
2025-05-26 14:28:28,778 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\declarations\web.d.ts (231,967 bytes)
2025-05-26 14:28:28,808 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index-legacy.d.ts (168,429 bytes)
2025-05-26 14:28:28,809 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts (611,184 bytes)
2025-05-26 14:28:28,828 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts (52,155 bytes)
2025-05-26 14:28:28,829 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts (52,150 bytes)
2025-05-26 14:28:28,830 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\buffer.d.ts (107,224 bytes)
2025-05-26 14:28:28,831 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\child_process.d.ts (67,998 bytes)
2025-05-26 14:28:28,831 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\crypto.d.ts (183,978 bytes)
2025-05-26 14:28:28,831 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs.d.ts (182,973 bytes)
2025-05-26 14:28:28,832 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http.d.ts (79,056 bytes)
2025-05-26 14:28:28,832 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http2.d.ts (113,815 bytes)
2025-05-26 14:28:28,832 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\inspector.d.ts (125,369 bytes)
2025-05-26 14:28:28,832 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\process.d.ts (73,684 bytes)
2025-05-26 14:28:28,833 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\stream.d.ts (70,767 bytes)
2025-05-26 14:28:28,833 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\tls.d.ts (54,923 bytes)
2025-05-26 14:28:28,833 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\util.d.ts (82,275 bytes)
2025-05-26 14:28:28,834 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts (53,450 bytes)
2025-05-26 14:28:28,835 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\buffer.d.ts (107,224 bytes)
2025-05-26 14:28:28,835 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\child_process.d.ts (67,998 bytes)
2025-05-26 14:28:28,835 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\crypto.d.ts (183,913 bytes)
2025-05-26 14:28:28,837 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs.d.ts (182,973 bytes)
2025-05-26 14:28:28,837 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http.d.ts (79,056 bytes)
2025-05-26 14:28:28,837 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http2.d.ts (113,815 bytes)
2025-05-26 14:28:28,837 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\inspector.d.ts (125,369 bytes)
2025-05-26 14:28:28,838 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\process.d.ts (73,684 bytes)
2025-05-26 14:28:28,839 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\stream.d.ts (72,452 bytes)
2025-05-26 14:28:28,839 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\tls.d.ts (54,923 bytes)
2025-05-26 14:28:28,840 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\util.d.ts (82,262 bytes)
2025-05-26 14:28:28,841 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts (53,380 bytes)
2025-05-26 14:28:28,847 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\dist\generated\ast-spec.d.ts (68,378 bytes)
2025-05-26 14:28:28,852 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\_ts3.4\dist\generated\ast-spec.d.ts (70,137 bytes)
2025-05-26 14:28:28,972 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts (816,786 bytes)
2025-05-26 14:28:28,972 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.es5.d.ts (213,196 bytes)
2025-05-26 14:28:28,972 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts (272,256 bytes)
2025-05-26 14:28:28,972 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\protocol.d.ts (107,934 bytes)
2025-05-26 14:28:28,972 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts (747,710 bytes)
2025-05-26 14:28:28,972 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts (598,789 bytes)
2025-05-26 14:28:28,972 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts (598,776 bytes)
2025-05-26 14:28:28,985 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts (335,559 bytes)
2025-05-26 14:28:28,995 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/ets/kits
2025-05-26 14:28:29,005 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/api
2025-05-26 14:28:29,006 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 14:28:29,006 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 14:28:29,010 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 14:28:29,010 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 14:28:29,010 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 14:28:29,011 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 14:28:29,011 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 14:28:29,011 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 14:28:29,011 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 14:28:29,012 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 14:28:29,013 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 14:28:29,013 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 14:28:29,013 - ComprehensiveRealSystemTest - INFO - Found file: Information/default/hms/ets/api\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 14:28:29,014 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/kits
2025-05-26 14:28:29,015 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/hms/ets/component
2025-05-26 14:28:29,015 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/ets/build-tools
2025-05-26 14:28:29,015 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/openharmony/native
2025-05-26 14:28:29,126 - ComprehensiveRealSystemTest - INFO - Searching in: Information/default/hms/native
2025-05-26 14:28:29,161 - ComprehensiveRealSystemTest - INFO - Searching in: comprehensive_dataset
2025-05-26 14:28:29,163 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\basic_ui\text_input.d.ts (62,924 bytes)
2025-05-26 14:28:29,163 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\container\swiper.d.ts (55,612 bytes)
2025-05-26 14:28:29,163 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\device\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-26 14:28:29,163 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\device\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-26 14:28:29,163 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\device\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-26 14:28:29,163 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\device\@ohos.sensor.d.ts (160,773 bytes)
2025-05-26 14:28:29,163 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\device\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-26 14:28:29,167 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\form\text_input.d.ts (62,924 bytes)
2025-05-26 14:28:29,169 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-26 14:28:29,170 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-26 14:28:29,170 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-26 14:28:29,171 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-26 14:28:29,171 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.iap.d.ts (88,545 bytes)
2025-05-26 14:28:29,172 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-26 14:28:29,172 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-26 14:28:29,172 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.core.weather.d.ts (79,418 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.health.store.d.ts (346,063 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\hms\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\multimedia\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\multimedia\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-26 14:28:29,173 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-26 14:28:29,178 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\navigation\navigation.d.ts (83,995 bytes)
2025-05-26 14:28:29,178 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\network\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-26 14:28:29,178 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\network\@ohos.net.http.d.ts (123,216 bytes)
2025-05-26 14:28:29,179 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\network\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-26 14:28:29,179 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\network\@ohos.request.d.ts (192,677 bytes)
2025-05-26 14:28:29,179 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\storage\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-26 14:28:29,180 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-26 14:28:29,180 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\storage\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-26 14:28:29,180 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-26 14:28:29,181 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\storage\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-26 14:28:29,181 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-26 14:28:29,182 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\storage\@ohos.fileio.d.ts (56,377 bytes)
2025-05-26 14:28:29,182 - ComprehensiveRealSystemTest - INFO - Found file: comprehensive_dataset\system\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-26 14:28:29,182 - ComprehensiveRealSystemTest - INFO - Selected 30 diverse ArkTS files for testing
2025-05-26 14:28:29,182 - ComprehensiveRealSystemTest - INFO - File size range: 50,405 - 842,681 bytes
2025-05-26 14:28:29,182 - ComprehensiveRealSystemTest - INFO - Average file size: 279,607 bytes
2025-05-26 14:28:29,182 - ComprehensiveRealSystemTest - INFO - Found 30 large files for testing
2025-05-26 14:28:29,182 - ComprehensiveRealSystemTest - INFO - Step 2: Testing parsing capabilities...
2025-05-26 14:28:29,182 - ComprehensiveRealSystemTest - INFO - === Testing Parsing Capabilities ===
2025-05-26 14:28:29,190 - ComprehensiveRealSystemTest - INFO - Parsing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 14:28:29,252 - ComprehensiveRealSystemTest - INFO - Parsed 196 symbols in 0.062s
2025-05-26 14:28:29,252 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'type': 1, 'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 1, 'union_type': 176, 'intersection_type': 1}
2025-05-26 14:28:29,252 - ComprehensiveRealSystemTest - INFO - Parsing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 14:28:29,322 - ComprehensiveRealSystemTest - INFO - Parsed 210 symbols in 0.070s
2025-05-26 14:28:29,322 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'interface': 14, 'const': 1, 'callback_type': 2, 'union_type': 190, 'intersection_type': 1}
2025-05-26 14:28:29,322 - ComprehensiveRealSystemTest - INFO - Parsing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 14:28:29,490 - ComprehensiveRealSystemTest - INFO - Parsed 2293 symbols in 0.168s
2025-05-26 14:28:29,490 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 45, 'interface': 1244, 'type': 185, 'function': 50, 'namespace': 110, 'enum': 41, 'const': 445, 'reexport_all': 1, 'callback_type': 12, 'union_type': 145, 'intersection_type': 12, 'export_assignment': 3}
2025-05-26 14:28:29,490 - ComprehensiveRealSystemTest - INFO - Parsing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 14:28:29,547 - ComprehensiveRealSystemTest - INFO - Parsed 147 symbols in 0.057s
2025-05-26 14:28:29,547 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'const': 88, 'union_type': 57}
2025-05-26 14:28:29,547 - ComprehensiveRealSystemTest - INFO - Parsing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 14:28:29,702 - ComprehensiveRealSystemTest - INFO - Parsed 2009 symbols in 0.155s
2025-05-26 14:28:29,702 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 3}
2025-05-26 14:28:29,702 - ComprehensiveRealSystemTest - INFO - Parsing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 14:28:29,849 - ComprehensiveRealSystemTest - INFO - Parsed 2008 symbols in 0.147s
2025-05-26 14:28:29,849 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'const': 429, 'reexport_all': 1, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 2}
2025-05-26 14:28:29,849 - ComprehensiveRealSystemTest - INFO - Parsing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 14:28:29,978 - ComprehensiveRealSystemTest - INFO - Parsed 1940 symbols in 0.129s
2025-05-26 14:28:29,978 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 27, 'interface': 1106, 'type': 169, 'function': 36, 'namespace': 41, 'enum': 39, 'const': 363, 'reexport_all': 1, 'callback_type': 12, 'union_type': 133, 'intersection_type': 11, 'export_assignment': 2}
2025-05-26 14:28:29,978 - ComprehensiveRealSystemTest - INFO - Parsing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 14:28:30,063 - ComprehensiveRealSystemTest - INFO - Parsed 243 symbols in 0.085s
2025-05-26 14:28:30,063 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2}
2025-05-26 14:28:30,063 - ComprehensiveRealSystemTest - INFO - Parsing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 14:28:30,144 - ComprehensiveRealSystemTest - INFO - Parsed 244 symbols in 0.080s
2025-05-26 14:28:30,144 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'const': 40, 'callback_type': 12, 'union_type': 2, 'component': 1}
2025-05-26 14:28:30,144 - ComprehensiveRealSystemTest - INFO - Parsing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 14:28:30,248 - ComprehensiveRealSystemTest - INFO - Parsed 1663 symbols in 0.104s
2025-05-26 14:28:30,248 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'const': 350, 'reexport_all': 1, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 2}
2025-05-26 14:28:30,248 - ComprehensiveRealSystemTest - INFO - Parsing file 11/30: Information/default/openharmony/ets/api\@ohos.UiTest.d.ts
2025-05-26 14:28:30,276 - ComprehensiveRealSystemTest - INFO - Parsed 21 symbols in 0.027s
2025-05-26 14:28:30,276 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 8, 'interface': 5, 'enum': 6, 'const': 2}
2025-05-26 14:28:30,276 - ComprehensiveRealSystemTest - INFO - Parsing file 12/30: Information/default/hms/ets/api\@hms.core.map.map.d.ts
2025-05-26 14:28:30,297 - ComprehensiveRealSystemTest - INFO - Parsed 41 symbols in 0.021s
2025-05-26 14:28:30,297 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 18, 'interface': 21, 'export_assignment': 1}
2025-05-26 14:28:30,297 - ComprehensiveRealSystemTest - INFO - Parsing file 13/30: comprehensive_dataset\hms\@hms.core.map.map.d.ts
2025-05-26 14:28:30,325 - ComprehensiveRealSystemTest - INFO - Parsed 41 symbols in 0.027s
2025-05-26 14:28:30,325 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 18, 'interface': 21, 'export_assignment': 1}
2025-05-26 14:28:30,325 - ComprehensiveRealSystemTest - INFO - Parsing file 14/30: Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts
2025-05-26 14:28:30,347 - ComprehensiveRealSystemTest - INFO - Parsed 28 symbols in 0.022s
2025-05-26 14:28:30,347 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 22, 'interface': 4, 'export_assignment': 1}
2025-05-26 14:28:30,347 - ComprehensiveRealSystemTest - INFO - Parsing file 15/30: Information/default/openharmony/ets/api\@ohos.contact.d.ts
2025-05-26 14:28:30,363 - ComprehensiveRealSystemTest - INFO - Parsed 22 symbols in 0.017s
2025-05-26 14:28:30,363 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 17, 'interface': 3, 'export_assignment': 1}
2025-05-26 14:28:30,363 - ComprehensiveRealSystemTest - INFO - Parsing file 16/30: Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts
2025-05-26 14:28:30,394 - ComprehensiveRealSystemTest - INFO - Parsed 68 symbols in 0.030s
2025-05-26 14:28:30,394 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 24, 'interface': 40, 'enum': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 14:28:30,394 - ComprehensiveRealSystemTest - INFO - Parsing file 17/30: comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts
2025-05-26 14:28:30,414 - ComprehensiveRealSystemTest - INFO - Parsed 68 symbols in 0.020s
2025-05-26 14:28:30,414 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 24, 'interface': 40, 'enum': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-26 14:28:30,414 - ComprehensiveRealSystemTest - INFO - Parsing file 18/30: Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts
2025-05-26 14:28:30,440 - ComprehensiveRealSystemTest - INFO - Parsed 28 symbols in 0.025s
2025-05-26 14:28:30,440 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 5, 'interface': 21, 'export_assignment': 1}
2025-05-26 14:28:30,441 - ComprehensiveRealSystemTest - INFO - Parsing file 19/30: comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts
2025-05-26 14:28:30,464 - ComprehensiveRealSystemTest - INFO - Parsed 28 symbols in 0.023s
2025-05-26 14:28:30,464 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 5, 'interface': 21, 'export_assignment': 1}
2025-05-26 14:28:30,464 - ComprehensiveRealSystemTest - INFO - Parsing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts
2025-05-26 14:28:30,480 - ComprehensiveRealSystemTest - INFO - Parsed 82 symbols in 0.017s
2025-05-26 14:28:30,480 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 80, 'export_assignment': 1}
2025-05-26 14:28:30,480 - ComprehensiveRealSystemTest - INFO - Parsing file 21/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts
2025-05-26 14:28:30,493 - ComprehensiveRealSystemTest - INFO - Parsed 25 symbols in 0.012s
2025-05-26 14:28:30,493 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'const': 24, 'reexport_all': 1}
2025-05-26 14:28:30,493 - ComprehensiveRealSystemTest - INFO - Parsing file 22/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts
2025-05-26 14:28:30,498 - ComprehensiveRealSystemTest - INFO - Parsed 25 symbols in 0.005s
2025-05-26 14:28:30,498 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'const': 24, 'reexport_all': 1}
2025-05-26 14:28:30,498 - ComprehensiveRealSystemTest - INFO - Parsing file 23/30: Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets
2025-05-26 14:28:30,509 - ComprehensiveRealSystemTest - INFO - Parsed 44 symbols in 0.011s
2025-05-26 14:28:30,509 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 4, 'interface': 32, 'namespace': 1, 'enum': 5, 'component': 1, 'decorator': 1}
2025-05-26 14:28:30,510 - ComprehensiveRealSystemTest - INFO - Parsing file 24/30: comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets
2025-05-26 14:28:30,514 - ComprehensiveRealSystemTest - INFO - Parsed 44 symbols in 0.004s
2025-05-26 14:28:30,514 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 4, 'interface': 32, 'namespace': 1, 'enum': 5, 'component': 1, 'decorator': 1}
2025-05-26 14:28:30,514 - ComprehensiveRealSystemTest - INFO - Parsing file 25/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts
2025-05-26 14:28:30,528 - ComprehensiveRealSystemTest - INFO - Parsed 83 symbols in 0.014s
2025-05-26 14:28:30,528 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 4, 'namespace': 7, 'interface': 56, 'callback_type': 1, 'union_type': 11, 'intersection_type': 4}
2025-05-26 14:28:30,528 - ComprehensiveRealSystemTest - INFO - Parsing file 26/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts
2025-05-26 14:28:30,542 - ComprehensiveRealSystemTest - INFO - Parsed 2 symbols in 0.013s
2025-05-26 14:28:30,543 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'interface': 1, 'class': 1}
2025-05-26 14:28:30,543 - ComprehensiveRealSystemTest - INFO - Parsing file 27/30: Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts
2025-05-26 14:28:30,546 - ComprehensiveRealSystemTest - INFO - Parsed 1 symbols in 0.003s
2025-05-26 14:28:30,546 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'enum': 1}
2025-05-26 14:28:30,546 - ComprehensiveRealSystemTest - INFO - Parsing file 28/30: Information/default/openharmony/ets/api\@ohos.display.d.ts
2025-05-26 14:28:30,553 - ComprehensiveRealSystemTest - INFO - Parsed 20 symbols in 0.008s
2025-05-26 14:28:30,553 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 18, 'export_assignment': 1}
2025-05-26 14:28:30,553 - ComprehensiveRealSystemTest - INFO - Parsing file 29/30: Information/default/openharmony/ets/api\@ohos.router.d.ts
2025-05-26 14:28:30,562 - ComprehensiveRealSystemTest - INFO - Parsed 7 symbols in 0.009s
2025-05-26 14:28:30,562 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 4, 'enum': 1, 'export_assignment': 1}
2025-05-26 14:28:30,562 - ComprehensiveRealSystemTest - INFO - Parsing file 30/30: Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts
2025-05-26 14:28:30,571 - ComprehensiveRealSystemTest - INFO - Parsed 21 symbols in 0.009s
2025-05-26 14:28:30,571 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 5, 'interface': 14, 'export_assignment': 1}
2025-05-26 14:28:30,574 - ComprehensiveRealSystemTest - INFO - Parsing completed: 30/30 files successful
2025-05-26 14:28:30,574 - ComprehensiveRealSystemTest - INFO - Total symbols parsed: 11652
2025-05-26 14:28:30,575 - ComprehensiveRealSystemTest - INFO - Symbol types found: {'type': 964, 'namespace': 413, 'interface': 5928, 'const': 2236, 'callback_type': 82, 'union_type': 1109, 'intersection_type': 49, 'class': 346, 'function': 204, 'enum': 285, 'reexport_all': 7, 'export_assignment': 24, 'component': 3, 'decorator': 2}
2025-05-26 14:28:30,575 - ComprehensiveRealSystemTest - INFO - Step 3: Testing indexing capabilities...
2025-05-26 14:28:30,575 - ComprehensiveRealSystemTest - INFO - === Testing Indexing Capabilities ===
2025-05-26 14:28:31,999 - ComprehensiveRealSystemTest - INFO - Qdrant collection created successfully
2025-05-26 14:28:31,999 - ComprehensiveRealSystemTest - INFO - Indexing file 1/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 14:28:36,746 - ComprehensiveRealSystemTest - INFO - Indexed 196 symbols in 4.747s
2025-05-26 14:28:36,746 - ComprehensiveRealSystemTest - INFO - Indexing file 2/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-26 14:28:41,429 - ComprehensiveRealSystemTest - INFO - Indexed 210 symbols in 4.683s
2025-05-26 14:28:41,430 - ComprehensiveRealSystemTest - INFO - Indexing file 3/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 14:29:54,326 - ComprehensiveRealSystemTest - INFO - Indexed 2293 symbols in 72.896s
2025-05-26 14:29:54,326 - ComprehensiveRealSystemTest - INFO - Indexing file 4/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-26 14:30:00,688 - ComprehensiveRealSystemTest - INFO - Indexed 147 symbols in 6.363s
2025-05-26 14:30:00,688 - ComprehensiveRealSystemTest - INFO - Indexing file 5/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-26 14:31:08,381 - ComprehensiveRealSystemTest - INFO - Indexed 2009 symbols in 67.692s
2025-05-26 14:31:08,381 - ComprehensiveRealSystemTest - INFO - Indexing file 6/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-26 14:32:17,798 - ComprehensiveRealSystemTest - INFO - Indexed 2008 symbols in 69.417s
2025-05-26 14:32:17,799 - ComprehensiveRealSystemTest - INFO - Indexing file 7/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-26 14:33:21,685 - ComprehensiveRealSystemTest - INFO - Indexed 1940 symbols in 63.887s
2025-05-26 14:33:21,685 - ComprehensiveRealSystemTest - INFO - Indexing file 8/30: Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-26 14:33:27,121 - ComprehensiveRealSystemTest - INFO - Indexed 243 symbols in 5.435s
2025-05-26 14:33:27,122 - ComprehensiveRealSystemTest - INFO - Indexing file 9/30: Information/default/openharmony/ets/component\common.d.ts
2025-05-26 14:33:32,760 - ComprehensiveRealSystemTest - INFO - Indexed 244 symbols in 5.638s
2025-05-26 14:33:32,760 - ComprehensiveRealSystemTest - INFO - Indexing file 10/30: Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-26 14:34:30,271 - ComprehensiveRealSystemTest - INFO - Indexed 1663 symbols in 57.511s
2025-05-26 14:34:30,272 - ComprehensiveRealSystemTest - INFO - Indexing file 11/30: Information/default/openharmony/ets/api\@ohos.UiTest.d.ts
2025-05-26 14:34:30,948 - ComprehensiveRealSystemTest - INFO - Indexed 21 symbols in 0.676s
2025-05-26 14:34:30,948 - ComprehensiveRealSystemTest - INFO - Indexing file 12/30: Information/default/hms/ets/api\@hms.core.map.map.d.ts
2025-05-26 14:34:32,102 - ComprehensiveRealSystemTest - INFO - Indexed 41 symbols in 1.154s
2025-05-26 14:34:32,102 - ComprehensiveRealSystemTest - INFO - Indexing file 13/30: comprehensive_dataset\hms\@hms.core.map.map.d.ts
2025-05-26 14:34:33,122 - ComprehensiveRealSystemTest - INFO - Indexed 41 symbols in 1.020s
2025-05-26 14:34:33,122 - ComprehensiveRealSystemTest - INFO - Indexing file 14/30: Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts
2025-05-26 14:34:33,786 - ComprehensiveRealSystemTest - INFO - Indexed 28 symbols in 0.663s
2025-05-26 14:34:33,786 - ComprehensiveRealSystemTest - INFO - Indexing file 15/30: Information/default/openharmony/ets/api\@ohos.contact.d.ts
2025-05-26 14:34:34,480 - ComprehensiveRealSystemTest - INFO - Indexed 22 symbols in 0.694s
2025-05-26 14:34:34,480 - ComprehensiveRealSystemTest - INFO - Indexing file 16/30: Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts
2025-05-26 14:34:36,562 - ComprehensiveRealSystemTest - INFO - Indexed 68 symbols in 2.082s
2025-05-26 14:34:36,562 - ComprehensiveRealSystemTest - INFO - Indexing file 17/30: comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts
2025-05-26 14:34:38,374 - ComprehensiveRealSystemTest - INFO - Indexed 68 symbols in 1.811s
2025-05-26 14:34:38,374 - ComprehensiveRealSystemTest - INFO - Indexing file 18/30: Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts
2025-05-26 14:34:39,208 - ComprehensiveRealSystemTest - INFO - Indexed 28 symbols in 0.835s
2025-05-26 14:34:39,208 - ComprehensiveRealSystemTest - INFO - Indexing file 19/30: comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts
2025-05-26 14:34:40,011 - ComprehensiveRealSystemTest - INFO - Indexed 28 symbols in 0.802s
2025-05-26 14:34:40,011 - ComprehensiveRealSystemTest - INFO - Indexing file 20/30: Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts
2025-05-26 14:34:41,958 - ComprehensiveRealSystemTest - INFO - Indexed 82 symbols in 1.947s
2025-05-26 14:34:41,958 - ComprehensiveRealSystemTest - INFO - Indexing file 21/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts
2025-05-26 14:34:42,783 - ComprehensiveRealSystemTest - INFO - Indexed 25 symbols in 0.826s
2025-05-26 14:34:42,783 - ComprehensiveRealSystemTest - INFO - Indexing file 22/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts
2025-05-26 14:34:43,683 - ComprehensiveRealSystemTest - INFO - Indexed 25 symbols in 0.899s
2025-05-26 14:34:43,683 - ComprehensiveRealSystemTest - INFO - Indexing file 23/30: Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets
2025-05-26 14:34:44,742 - ComprehensiveRealSystemTest - INFO - Indexed 44 symbols in 1.058s
2025-05-26 14:34:44,743 - ComprehensiveRealSystemTest - INFO - Indexing file 24/30: comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets
2025-05-26 14:34:45,921 - ComprehensiveRealSystemTest - INFO - Indexed 44 symbols in 1.178s
2025-05-26 14:34:45,922 - ComprehensiveRealSystemTest - INFO - Indexing file 25/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts
2025-05-26 14:34:48,667 - ComprehensiveRealSystemTest - INFO - Indexed 83 symbols in 2.746s
2025-05-26 14:34:48,667 - ComprehensiveRealSystemTest - INFO - Indexing file 26/30: Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts
2025-05-26 14:34:48,766 - ComprehensiveRealSystemTest - INFO - Indexed 2 symbols in 0.090s
2025-05-26 14:34:48,766 - ComprehensiveRealSystemTest - INFO - Indexing file 27/30: Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts
2025-05-26 14:34:48,809 - ComprehensiveRealSystemTest - INFO - Indexed 1 symbols in 0.028s
2025-05-26 14:34:48,809 - ComprehensiveRealSystemTest - INFO - Indexing file 28/30: Information/default/openharmony/ets/api\@ohos.display.d.ts
2025-05-26 14:34:49,304 - ComprehensiveRealSystemTest - INFO - Indexed 20 symbols in 0.495s
2025-05-26 14:34:49,304 - ComprehensiveRealSystemTest - INFO - Indexing file 29/30: Information/default/openharmony/ets/api\@ohos.router.d.ts
2025-05-26 14:34:49,469 - ComprehensiveRealSystemTest - INFO - Indexed 7 symbols in 0.165s
2025-05-26 14:34:49,469 - ComprehensiveRealSystemTest - INFO - Indexing file 30/30: Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts
2025-05-26 14:34:49,994 - ComprehensiveRealSystemTest - INFO - Indexed 21 symbols in 0.524s
2025-05-26 14:34:49,995 - ComprehensiveRealSystemTest - INFO - Indexing completed: 30/30 files successful
2025-05-26 14:34:49,995 - ComprehensiveRealSystemTest - INFO - Total symbols indexed: 11652
2025-05-26 14:34:49,996 - ComprehensiveRealSystemTest - INFO - Step 4: Testing agent capabilities...
2025-05-26 14:34:49,996 - ComprehensiveRealSystemTest - INFO - === Testing Agent Capabilities ===
2025-05-26 14:34:50,021 - ComprehensiveRealSystemTest - INFO - Testing query: 'Button' (type: component)
2025-05-26 14:34:50,063 - ComprehensiveRealSystemTest - INFO - Query successful in 0.043s, results length: 602
2025-05-26 14:34:50,063 - ComprehensiveRealSystemTest - INFO - Testing query: 'Dialog' (type: component)
2025-05-26 14:34:50,086 - ComprehensiveRealSystemTest - INFO - Query successful in 0.022s, results length: 17
2025-05-26 14:34:50,086 - ComprehensiveRealSystemTest - INFO - Testing query: 'Text' (type: component)
2025-05-26 14:34:50,130 - ComprehensiveRealSystemTest - INFO - Query successful in 0.045s, results length: 17
2025-05-26 14:34:50,130 - ComprehensiveRealSystemTest - INFO - Testing query: 'Image' (type: component)
2025-05-26 14:34:50,151 - ComprehensiveRealSystemTest - INFO - Query successful in 0.021s, results length: 17
2025-05-26 14:34:50,151 - ComprehensiveRealSystemTest - INFO - Testing query: 'List' (type: component)
2025-05-26 14:34:50,188 - ComprehensiveRealSystemTest - INFO - Query successful in 0.036s, results length: 17
2025-05-26 14:34:50,188 - ComprehensiveRealSystemTest - INFO - Testing query: 'UIAbility' (type: api)
2025-05-26 14:34:50,219 - ComprehensiveRealSystemTest - INFO - Query successful in 0.031s, results length: 17
2025-05-26 14:34:50,225 - ComprehensiveRealSystemTest - INFO - Testing query: 'router' (type: api)
2025-05-26 14:34:50,252 - ComprehensiveRealSystemTest - INFO - Query successful in 0.026s, results length: 549
2025-05-26 14:34:50,252 - ComprehensiveRealSystemTest - INFO - Testing query: 'preferences' (type: api)
2025-05-26 14:34:50,297 - ComprehensiveRealSystemTest - INFO - Query successful in 0.046s, results length: 1932
2025-05-26 14:34:50,297 - ComprehensiveRealSystemTest - INFO - Testing query: 'http' (type: api)
2025-05-26 14:34:50,334 - ComprehensiveRealSystemTest - INFO - Query successful in 0.036s, results length: 1786
2025-05-26 14:34:50,334 - ComprehensiveRealSystemTest - INFO - Testing query: 'bluetooth' (type: api)
2025-05-26 14:34:50,368 - ComprehensiveRealSystemTest - INFO - Query successful in 0.035s, results length: 17
2025-05-26 14:34:50,368 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.app.ability.UIAbility' (type: import_path)
2025-05-26 14:34:50,399 - ComprehensiveRealSystemTest - INFO - Query successful in 0.030s, results length: 17
2025-05-26 14:34:50,399 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.router' (type: import_path)
2025-05-26 14:34:50,431 - ComprehensiveRealSystemTest - INFO - Query successful in 0.032s, results length: 17
2025-05-26 14:34:50,431 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.data.preferences' (type: import_path)
2025-05-26 14:34:50,464 - ComprehensiveRealSystemTest - INFO - Query successful in 0.033s, results length: 17
2025-05-26 14:34:50,465 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.net.http' (type: import_path)
2025-05-26 14:34:50,499 - ComprehensiveRealSystemTest - INFO - Query successful in 0.034s, results length: 17
2025-05-26 14:34:50,499 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.bluetooth' (type: import_path)
2025-05-26 14:34:50,532 - ComprehensiveRealSystemTest - INFO - Query successful in 0.033s, results length: 17
2025-05-26 14:34:50,532 - ComprehensiveRealSystemTest - INFO - Testing query: 'file system' (type: api)
2025-05-26 14:34:50,614 - ComprehensiveRealSystemTest - INFO - Query successful in 0.081s, results length: 1095
2025-05-26 14:34:50,614 - ComprehensiveRealSystemTest - INFO - Testing query: 'data storage' (type: api)
2025-05-26 14:34:50,650 - ComprehensiveRealSystemTest - INFO - Query successful in 0.036s, results length: 17
2025-05-26 14:34:50,650 - ComprehensiveRealSystemTest - INFO - Testing query: 'network request' (type: api)
2025-05-26 14:34:50,683 - ComprehensiveRealSystemTest - INFO - Query successful in 0.033s, results length: 17
2025-05-26 14:34:50,683 - ComprehensiveRealSystemTest - INFO - Testing query: 'device sensor' (type: api)
2025-05-26 14:34:50,714 - ComprehensiveRealSystemTest - INFO - Query successful in 0.031s, results length: 17
2025-05-26 14:34:50,714 - ComprehensiveRealSystemTest - INFO - Testing query: 'multimedia audio' (type: api)
2025-05-26 14:34:50,736 - ComprehensiveRealSystemTest - INFO - Query successful in 0.022s, results length: 17
2025-05-26 14:34:50,750 - ComprehensiveRealSystemTest - INFO - Agent testing completed: 20/20 queries successful
2025-05-26 14:34:50,751 - ComprehensiveRealSystemTest - INFO - === Generating Comprehensive Report ===
2025-05-26 14:34:50,753 - ComprehensiveRealSystemTest - INFO - Comprehensive report saved to test_results/Test_ComprehensiveRealSystem_Results/
2025-05-26 14:34:50,753 - ComprehensiveRealSystemTest - INFO - Test completed successfully!
2025-05-26 14:34:50,754 - ComprehensiveRealSystemTest - INFO - Comprehensive test completed in 382.04 seconds
