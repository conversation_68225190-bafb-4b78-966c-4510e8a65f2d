[{"symbol_name": "resolveAny", "symbol_type": "namespace", "module_name": "dns", "is_default": false, "is_ets": false, "description": "Uses the DNS protocol to resolve all records (also known as `ANY` or `` query). The `ret` argument passed to the `callback` function will be an array containing various types of records. Each object has a property `type` that indicates the type of the current record. And depending on the `type`, additional properties will be present on the object: <omitted> Here is an example of the `ret` object passed to the callback: ```js [ { type: 'A', address: '127.0.0.1', ttl: 299 }, { type: 'CNAME', value: 'example.com' }, { type: 'MX', exchange: 'alt4.aspmx.l.example.com', priority: 50 }, { type: 'NS', value: 'ns1.example.com' }, { type: 'TXT', entries: [ 'v=spf1 include:_spf.example.com ~all' ] }, { type: 'SOA', nsname: 'ns1.example.com', hostmaster: 'admin.example.com', serial: 156696742, refresh: 900, retry: 900, expire: 1800, minttl: 60 } ] ``` DNS server operators may choose not to respond to `ANY`queries. It may be better to call individual methods like {@link resolve4},{@link resolveMx}, and so on. For more details, see [RFC 8482](https://tools.ietf.org/html/rfc8482). export function resolveAny(hostname: string, callback: (err: NodeJS.ErrnoException | null, addresses: AnyRecord[]) => void): void;", "import_statement": "import { resolveAny } from 'dns';", "parent_symbol": null, "is_nested": false, "score": 0.5905324}, {"symbol_name": "fstat", "symbol_type": "namespace", "module_name": "fs", "is_default": false, "is_ets": false, "description": "Invokes the callback with the `fs.Stats` for the file descriptor. See the POSIX [`fstat(2)`](http://man7.org/linux/man-pages/man2/fstat.2.html) documentation for more detail. @since v0.1.95 export function fstat(fd: number, callback: (err: NodeJS.ErrnoException | null, stats: Stats) => void): void; export function fstat( fd: number, options: | (StatOptions & { bigint?: false | undefined; }) | undefined, callback: (err: NodeJS.ErrnoException | null, stats: Stats) => void ): void; export function fstat( fd: number, options: StatOptions & { bigint: true; }, callback: (err: NodeJS.ErrnoException | null, stats: BigIntStats) => void ): void; export function fstat(fd: number, options: StatOptions | undefined, callback: (err: NodeJS.ErrnoException | null, stats: Stats | BigIntStats) => void): void;", "import_statement": "import { fstat } from 'fs';", "parent_symbol": null, "is_nested": false, "score": 0.5632717}, {"symbol_name": "statfs", "symbol_type": "namespace", "module_name": "fs", "is_default": false, "is_ets": false, "description": "Asynchronous [`statfs(2)`](http://man7.org/linux/man-pages/man2/statfs.2.html). Returns information about the mounted file system which contains `path`. The callback gets two arguments `(err, stats)` where `stats`is an `fs.StatFs` object. In case of an error, the `err.code` will be one of `Common System Errors`. @since v19.6.0, v18.15.0 @param path A path to an existing file or directory on the file system to be queried. export function statfs(path: PathLike, callback: (err: NodeJS.ErrnoException | null, stats: StatsFs) => void): void; export function statfs( path: PathLike, options: | (StatFsOptions & { bigint?: false | undefined; }) | undefined, callback: (err: NodeJS.ErrnoException | null, stats: StatsFs) => void ): void; export function statfs( path: PathLike, options: StatFsOptions & { bigint: true; }, callback: (err: NodeJS.ErrnoException | null, stats: BigIntStatsFs) => void ): void; export function statfs(path: PathLike, options: StatFsOptions | undefined, callback: (err: NodeJS.ErrnoException | null, stats: StatsFs | BigIntStatsFs) => void): void;", "import_statement": "import { statfs } from 'fs';", "parent_symbol": null, "is_nested": false, "score": 0.5367615}]